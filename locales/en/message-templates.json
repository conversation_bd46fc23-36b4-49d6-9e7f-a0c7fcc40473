{"title": "Edit Message Template", "subtitle": "Modify template content, variables, and metadata", "sections": {"basicInfo": {"name": "Basic Info", "description": "Template name and description"}, "content": {"name": "Template Content", "description": "Message content and variables"}, "metadata": {"name": "<PERSON><PERSON><PERSON>", "description": "Tags and settings"}, "systemInfo": {"name": "System Info", "description": "Tracking information"}}, "fields": {"name": {"label": "Template Name", "placeholder": "Enter a descriptive name for this template", "description": "A clear, descriptive name that identifies the purpose of this template", "examples": "Examples: Welcome Message, Order Confirmation, Support Follow-up, Price Quote Response"}, "description": {"label": "Description", "placeholder": "Describe when and how this template should be used...", "description": "Detailed explanation of the template's purpose, use cases, and context", "examples": "Examples:\n• Welcome message sent to new customers after registration\n• Automated response for pricing inquiries with product details\n• Follow-up message after customer support interaction\n• Confirmation template for completed orders"}, "content": {"label": "Template Content", "placeholder": "Enter your message template here. Use {{variable}} for dynamic content...", "description": "The actual message content. Use {{variable}} syntax for dynamic placeholders that will be replaced with actual values.", "examples": "Examples:\n• Hello {{customerName}}, welcome to {{companyName}}!\n• Your order #{{orderNumber}} has been confirmed for {{amount}}\n• Hi {{name}}, thank you for contacting us about {{subject}}\n• The price for {{productName}} is {{price}} with {{discount}}% discount"}, "variables": {"label": "Variables", "placeholder": "List available variables separated by commas", "description": "Available variables that can be used in the template content. These will be replaced with actual values when the template is used.", "examples": "Examples:\n• customerName, email, phone, companyName\n• orderNumber, amount, productName, quantity\n• supportTicketId, subject, priority, assignedAgent\n• price, discount, currency, deliveryDate"}, "tags": {"label": "Tags", "placeholder": "Add tags for organization (optional)", "description": "Optional labels to help categorize and organize templates", "examples": "Examples: welcome, order, support, pricing, marketing, automated, urgent"}, "isActive": {"label": "Active", "description": "Enable or disable this template"}, "createdAt": {"label": "Created Date"}, "updatedAt": {"label": "Last Updated"}, "createdBy": {"label": "Created By"}}, "buttons": {"update": "Update Template", "create": "Create Template", "cancel": "Cancel"}, "validation": {"nameRequired": "Template name is required", "nameMinLength": "Template name must be at least 2 characters", "nameMaxLength": "Template name cannot exceed 100 characters", "descriptionMaxLength": "Description cannot exceed 500 characters", "contentRequired": "Template content is required", "contentMinLength": "Template content must be at least 10 characters"}, "messages": {"updateSuccess": "Message Template updated successfully", "createSuccess": "Message Template created successfully", "updateError": "Failed to update Message Template", "createError": "Failed to create Message Template", "fetchError": "Failed to fetch Message Template", "notFound": "Message Template not found"}}