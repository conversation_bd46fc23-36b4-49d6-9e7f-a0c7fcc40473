{"title": "Edit Contact", "subtitle": "Modify contact information and metadata", "sections": {"basicInfo": {"name": "Basic Info", "description": "Contact name and communication details"}, "contactDetails": {"name": "Contact Details", "description": "Phone, email, and address information"}, "metadata": {"name": "<PERSON><PERSON><PERSON>", "description": "Tags and notes"}, "systemInfo": {"name": "System Info", "description": "Tracking information"}}, "fields": {"name": {"label": "Contact Name", "placeholder": "Enter the full name of the contact", "description": "The complete name of the person or organization you're communicating with", "examples": "Examples: <PERSON>, <PERSON>, ABC Corporation, PT Maju Jaya"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number with country code", "description": "Primary phone number for WhatsApp communication. Include country code for international numbers.", "examples": "Examples:\n• ******-123-4567 (US format)\n• +62-812-3456-7890 (Indonesia format)\n• +44-20-7946-0958 (UK format)\n• +86-138-0013-8000 (China format)"}, "email": {"label": "Email Address", "placeholder": "Enter email address", "description": "Primary email address for contact communication and identification", "examples": "Examples: <EMAIL>, <EMAIL>, <EMAIL>"}, "address": {"label": "Address", "placeholder": "Enter complete address", "description": "Physical address or location information for the contact", "examples": "Examples:\n• 123 Main Street, New York, NY 10001, USA\n• Jl. Sudirman No. 45, Jakarta Pusat, Indonesia\n• 10 Downing Street, London SW1A 2AA, UK\n• Building 2, Floor 5, Business District"}, "notes": {"label": "Notes", "placeholder": "Add any additional notes about this contact...", "description": "Internal notes, preferences, or important information about the contact", "examples": "Examples:\n• Prefers communication in the morning\n• VIP customer - priority support\n• Interested in premium products\n• Previous order: #12345 on 2024-01-15\n• Speaks English and Indonesian"}, "tags": {"label": "Tags", "placeholder": "Add tags for organization (optional)", "description": "Labels to help categorize and organize contacts for better management", "examples": "Examples: customer, prospect, vip, supplier, partner, lead, inactive, priority"}, "isActive": {"label": "Active", "description": "Enable or disable this contact"}, "createdAt": {"label": "Created Date"}, "updatedAt": {"label": "Last Updated"}, "createdBy": {"label": "Created By"}}, "buttons": {"update": "Update Contact", "create": "Create Contact", "cancel": "Cancel"}, "validation": {"nameRequired": "Contact name is required", "nameMinLength": "Contact name must be at least 2 characters", "nameMaxLength": "Contact name cannot exceed 100 characters", "phoneRequired": "Phone number is required", "phoneFormat": "Please enter a valid phone number", "emailFormat": "Please enter a valid email address", "notesMaxLength": "Notes cannot exceed 1000 characters"}, "messages": {"updateSuccess": "Contact updated successfully", "createSuccess": "Contact created successfully", "updateError": "Failed to update contact", "createError": "Failed to create contact", "fetchError": "Failed to fetch contact", "notFound": "Contact not found"}}