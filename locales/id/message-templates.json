{"title": "Edit Template <PERSON>", "subtitle": "Ubah konten template, variabel, dan metadata", "sections": {"basicInfo": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> dan <PERSON><PERSON><PERSON> template"}, "content": {"name": "Konten Template", "description": "<PERSON><PERSON>n pesan dan variabel"}, "metadata": {"name": "<PERSON><PERSON><PERSON>", "description": "Tag dan pengaturan"}, "systemInfo": {"name": "Info Sistem", "description": "Informasi pelacakan"}}, "fields": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama deskriptif untuk template ini", "description": "<PERSON>a yang jelas dan deskriptif yang mengidentifikasi tujuan template ini", "examples": "Contoh: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Jelaskan kapan dan bagaimana template ini harus digunakan...", "description": "Penjelasan detail tentang tujuan template, ka<PERSON> pen<PERSON>, dan konteks", "examples": "Contoh:\n• Pesan selamat datang yang dikirim ke pelanggan baru setelah registrasi\n• Respon otomatis untuk pertanyaan harga dengan detail produk\n• Pesan tindak lanjut setelah interaksi dukungan pelanggan\n• Template konfirmasi untuk pesanan yang selesai"}, "content": {"label": "Konten Template", "placeholder": "Masukkan template pesan <PERSON><PERSON> di sini. Gunakan {{variabel}} untuk konten dinamis...", "description": "Konten pesan yang sebenarnya. Gunakan sintaks {{variabel}} untuk placeholder dinamis yang akan diganti dengan nilai aktual.", "examples": "Contoh:\n• Halo {{customerName}}, selamat datang di {{companyName}}!\n• <PERSON><PERSON><PERSON> #{{orderNumber}} telah dikonfirmasi sebesar {{amount}}\n• Hai {{name}}, terima kasih telah menghubungi kami tentang {{subject}}\n• Harga untuk {{productName}} adalah {{price}} dengan diskon {{discount}}%"}, "variables": {"label": "Variabel", "placeholder": "Daftar variabel yang tersedia dipisahkan dengan koma", "description": "Variabel yang tersedia yang dapat digunakan dalam konten template. Ini akan diganti dengan nilai aktual ketika template digunakan.", "examples": "Contoh:\n• customerName, email, phone, companyName\n• orderNumber, amount, productName, quantity\n• supportTicketId, subject, priority, assignedAgent\n• price, discount, currency, deliveryDate"}, "tags": {"label": "Tag", "placeholder": "Tambahkan tag untuk organisasi (opsional)", "description": "Label opsional untuk membantu mengkategorikan dan mengorganisir template", "examples": "Contoh: selamat-datang, pesanan, dukungan, harga, pema<PERSON>an, otomatis, mendesak"}, "isActive": {"label": "Aktif", "description": "Aktifkan atau nonaktifkan template ini"}, "createdAt": {"label": "Tanggal Dibuat"}, "updatedAt": {"label": "<PERSON><PERSON><PERSON>"}, "createdBy": {"label": "Dibuat Oleh"}}, "buttons": {"update": "<PERSON><PERSON><PERSON>", "create": "Buat Template", "cancel": "<PERSON><PERSON>"}, "validation": {"nameRequired": "<PERSON><PERSON> <PERSON> wajib diisi", "nameMinLength": "Nama template minimal 2 karakter", "nameMaxLength": "Nama template tidak boleh lebih dari 100 karakter", "descriptionMaxLength": "Deskripsi tidak boleh lebih dari 500 karakter", "contentRequired": "Konten template wajib diisi", "contentMinLength": "Konten template minimal 10 karakter"}, "messages": {"updateSuccess": "Template <PERSON><PERSON> be<PERSON>", "createSuccess": "Template <PERSON><PERSON> berhasil dibuat", "updateError": "<PERSON><PERSON> Template <PERSON>", "createError": "<PERSON><PERSON> membuat Template <PERSON>", "fetchError": "Gagal mengambil Template <PERSON>", "notFound": "Template <PERSON><PERSON> tidak di<PERSON>n"}}