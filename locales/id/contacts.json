{"title": "<PERSON>", "subtitle": "Ubah informasi kontak dan metadata", "sections": {"basicInfo": {"name": "<PERSON><PERSON>", "description": "<PERSON>a kontak dan detail komunikasi"}, "contactDetails": {"name": "Detail Ko<PERSON>k", "description": "Informasi telepon, email, dan alamat"}, "metadata": {"name": "<PERSON><PERSON><PERSON>", "description": "Tag dan catatan"}, "systemInfo": {"name": "Info Sistem", "description": "Informasi pelacakan"}}, "fields": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama lengkap kontak", "description": "<PERSON>a lengkap orang atau organisasi yang berkomunikasi dengan <PERSON>a", "examples": "Contoh: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CV <PERSON>"}, "phone": {"label": "Nomor Telepon", "placeholder": "Ma<PERSON>kkan nomor telepon dengan kode negara", "description": "Nomor telepon utama untuk komunikasi WhatsApp. Sertakan kode negara untuk nomor internasional.", "examples": "Contoh:\n• +62-812-3456-7890 (format Indonesia)\n• ******-123-4567 (format AS)\n• +44-20-7946-0958 (format Inggris)\n• +86-138-0013-8000 (format China)"}, "email": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> email", "description": "Alamat email utama untuk komunikasi dan identifikasi kontak", "examples": "Contoh: <EMAIL>, <EMAIL>, <EMAIL>"}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> alamat le<PERSON>p", "description": "<PERSON><PERSON><PERSON> atau informasi lokasi untuk kontak", "examples": "Contoh:\n• Jl<PERSON> No. 45, Jakarta Pusat, Indonesia\n• Jl. <PERSON> 123, Yogyakarta 55271\n• Gedung ABC Lt. 5, <PERSON><PERSON><PERSON>, Jakarta\n• Komplek Perumahan Indah Blok A No. 10"}, "notes": {"label": "Catatan", "placeholder": "Tambahkan catatan tambahan tentang kontak ini...", "description": "Catatan internal, preferensi, atau informasi penting tentang kontak", "examples": "Contoh:\n• Lebih suka komunikasi di pagi hari\n• Pelanggan VIP - dukungan prioritas\n• Tertarik dengan produk premium\n• Pesanan sebelumnya: #12345 pada 15-01-2024\n• Berbicara bahasa Indonesia dan Inggris"}, "tags": {"label": "Tag", "placeholder": "Tambahkan tag untuk organisasi (opsional)", "description": "Label untuk membantu mengkategorikan dan mengorganisir kontak untuk manajemen yang lebih baik", "examples": "Contoh: pelanggan, prospek, vip, supplier, mitra, lead, tidak-aktif, prioritas"}, "isActive": {"label": "Aktif", "description": "Aktifkan atau nonaktifkan kontak ini"}, "createdAt": {"label": "Tanggal Dibuat"}, "updatedAt": {"label": "<PERSON><PERSON><PERSON>"}, "createdBy": {"label": "Dibuat Oleh"}}, "buttons": {"update": "<PERSON><PERSON><PERSON>", "create": "Buat Kontak", "cancel": "<PERSON><PERSON>"}, "validation": {"nameRequired": "<PERSON><PERSON> kontak wajib diisi", "nameMinLength": "<PERSON><PERSON> k<PERSON>ak minimal 2 karakter", "nameMaxLength": "Nama kontak tidak boleh lebih dari 100 karakter", "phoneRequired": "Nomor telepon wajib diisi", "phoneFormat": "<PERSON><PERSON>an masukkan nomor telepon yang valid", "emailFormat": "<PERSON><PERSON><PERSON> ma<PERSON>kkan alamat email yang valid", "notesMaxLength": "Catatan tidak boleh lebih dari 1000 karakter"}, "messages": {"updateSuccess": "Kontak ber<PERSON><PERSON>", "createSuccess": "Kontak berhasil dibuat", "updateError": "<PERSON><PERSON> kontak", "createError": "<PERSON><PERSON> membuat kontak", "fetchError": "Gagal mengambil kontak", "notFound": "Kontak tidak ditemukan"}}