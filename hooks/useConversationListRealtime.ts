"use client"

import { useEffect, useCallback } from "react"
import { RealtimeConversationList, RealtimeConversationRoom } from "@/lib/realtime/model"
import { Conversation } from "@/lib/repositories/conversations"
import { ConversationMessage } from "@/lib/repositories/conversationMessages"
import { useAccountInfoContext } from "@/components/contexts/useAccountContext"

interface UseConversationListRealtimeProps {
  data: Conversation[]
  setData: (data: Conversation[]) => void
  selectedConversation: Conversation | null
  onSelectConversation: (conversation: Conversation) => void
  refetch: (() => void) | undefined
  onConversationUpdate?: (updatedConversation: Conversation) => void
}

export function useConversationListRealtime({
  data,
  setData,
  selectedConversation,
  onSelectConversation,
  refetch,
  onConversationUpdate,
}: UseConversationListRealtimeProps) {

  const { account } = useAccountInfoContext()

  function handleNewConversation(newData: Conversation): void {
    setData([newData, ...(data)])
    if (onConversationUpdate) {
      onConversationUpdate(newData)
    }
  }

  function handleConversationUpdate(updateData: Conversation): void {
    const oldData = data;

    const newData = oldData.map((conversation) => {
      if (conversation.id === updateData.id) {
        return updateData;
      }
      return conversation;
    })

    setData(newData)
    if (onConversationUpdate) {
      onConversationUpdate(updateData)
    }
  }

  useEffect(() => {
    if (!refetch) return
    const channel = RealtimeConversationList.subscribe(account.id)
    RealtimeConversationList.LISTEN_NEW_CONVERSATION(channel, handleNewConversation)
    RealtimeConversationList.LISTEN_UPDATE_COVERSATION(channel, handleConversationUpdate)
    return () => {
      channel.unbind_all()
      channel.unsubscribe()
    }
  }, [refetch, handleNewConversation, handleConversationUpdate])
}

