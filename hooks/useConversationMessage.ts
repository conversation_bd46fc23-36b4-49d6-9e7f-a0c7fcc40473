import { ConversationMessage } from "@/lib/repositories/conversationMessages"
import { Conversation } from "@/lib/repositories/conversations"
import { ConversationMessagesAPI } from "@/lib/services/conversationMessageApi"
import { useEffect, useRef, useState } from "react"

export type useConversationMessage = any

interface UseReceiveMessagesOptions {
  conversationId: string
}

const k_PERPAGE = 20

export function useConversationMessage({
  conversationId,
}: UseReceiveMessagesOptions) {
  const [messages, setMessages] = useState<ConversationMessage[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(0)

  // ✅ 1. Ref to store the latest messages
  const messagesRef = useRef<ConversationMessage[]>([])

  // ✅ 2. Sync the ref whenever messages update
  useEffect(() => {
    messagesRef.current = messages
  }, [messages])

  const fetchMessages = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await ConversationMessagesAPI.All(conversationId, {
        page: page + 1, // API uses 1-based pagination
        per_page: k_PERPAGE,
        search: undefined,
        sort: [{ field: "createdAt", direction: "DESC" }],
        filters: [],
      }).request()

      const reversedData = response.items?.reverse()

      if (reversedData) {
        let newData = reversedData
        if (page !== 0) {
          newData = [...messagesRef.current, ...reversedData]
        }

        setMessages(newData)
        setHasMore(newData.length < response.total)
      }
    } catch (err: any) {
      setError(err.message)
      setMessages([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMessages()
  }, [conversationId, page])

  // ✅ 3. Use messagesRef in the handler
  const handleNewMessage = (
    newConversationId: string,
    message: ConversationMessage
  ) => {
    if (newConversationId !== conversationId) return
    setMessages([...messagesRef.current, message])
  }

  return {
    messages,
    hasMore,
    page,
    setPage,
    loading,
    error,
    handleNewMessage,
  }
}
