"use client"

import { useState } from "react"
import { Conversation } from "@/lib/repositories/conversations"
import { toast } from "sonner"
import { ConversationsAPI } from "@/lib/services"
import { ConversationMessagesAPI } from "@/lib/services/conversationMessageApi"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales/useConversationAI"

interface UseConversationAIProps {
  conversation: Conversation
  onConversationUpdated?: (updatedConversation: Conversation) => void
}

export function useConversationAI({
  conversation,
  onConversationUpdated,
}: UseConversationAIProps) {
  const [isToggling, setIsToggling] = useState(false)
  const [isAnswering, setIsAnswering] = useState(false)
  const { t } = useLocalization("useConversationAI", locales)

  const toggleAI = async (enabled: boolean) => {
    setIsToggling(true)
    try {
      const response = await ConversationsAPI.Update(conversation.id, {
        isAiEnabled: enabled,
      }).request()

      onConversationUpdated?.(response)
      await createAILogMessage(enabled, conversation.id)
      toast.success(
        enabled ? t("ai_enabled") : t("ai_disabled")
      )
    } catch (error) {
      console.error("Error toggling AI:", error)
      toast.error(
        error instanceof Error ? error.message : t("toggle_error")
      )
    } finally {
      setIsToggling(false)
    }
  }

  const createAILogMessage = async (enabled: boolean, conversationId: string) => {
    try {
      const logContent = enabled
        ? t("ai_enable_log")
        : t("ai_disable_log")

      await ConversationMessagesAPI.Create(
        conversationId, {
        conversationId,
        content: logContent,
        messageType: "TEXT",
        category: "LOG",
      }).request()
    } catch (error) {
      console.error("Error creating AI log message:", error)
    }
  }

  const answerWithAI = async (messageId: string) => {
    setIsAnswering(true)
    try {
      const response = await ConversationsAPI.AIAnswer(conversation.id, messageId,).request()
      toast.success(t("ai_answer_success"))
    } catch (error) {
      console.error("Error generating AI answer:", error)
      toast.error(
        error instanceof Error ? error.message : t("ai_answer_error")
      )
    } finally {
      setIsAnswering(false)
    }
  }

  return {
    isToggling,
    isAnswering,
    toggleAI,
    answerWithAI,
  }
}
