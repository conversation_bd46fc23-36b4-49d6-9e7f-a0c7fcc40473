# Use the official Bun image
FROM oven/bun:1.2.10-alpine AS base

# Set working directory
WORKDIR /app

# Copy Bun lockfile and package manifest
COPY bun.lock package.json ./

# Install dependencies
RUN bun install --frozen-lockfile

# Copy the rest of the application
COPY . .

# Build (optional — depending on your project, e.g., Next.js, Vite, etc.)
# Comment this out if you don't have a build step
RUN bun run build

# Start fresh production image
FROM oven/bun:1.2.10-alpine AS runner

WORKDIR /app

# Copy everything from base
COPY --from=base /app ./

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
EXPOSE 3000

# Start the app
CMD ["bun", "start"]
