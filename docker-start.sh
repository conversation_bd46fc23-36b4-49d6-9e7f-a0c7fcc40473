#!/bin/bash

# CS AI App Docker Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status()    { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success()   { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning()   { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error()     { echo -e "${RED}[ERROR]${NC} $1"; }

# Ensure .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found!"
    if [ -f ".env.example" ]; then
        print_status "Copying .env.example to .env..."
        cp .env.example .env
        print_warning "Please edit .env before running."
        exit 1
    else
        print_error "No .env or .env.example file found!"
        exit 1
    fi
fi

# Parse flags
ENVIRONMENT="development"
DETACHED=""
FORCE_BUILD=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--production) ENVIRONMENT="production"; shift ;;
        -d|--detached)   DETACHED="-d"; shift ;;
        -b|--build)      FORCE_BUILD=true; shift ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "  -p, --production    Run in production mode"
            echo "  -d, --detached      Run in detached mode"
            echo "  -b, --build         Force rebuild of images"
            echo "  -h, --help          Show this help message"
            exit 0
            ;;
        *) print_error "Unknown option: $1"; exit 1 ;;
    esac
done

print_status "Starting CS AI App in $ENVIRONMENT mode..."

# Choose correct compose file
COMPOSE_FILE="docker-compose.yml"
[ "$ENVIRONMENT" = "production" ] && COMPOSE_FILE="docker-compose.prod.yml"

print_status "Using compose file: $COMPOSE_FILE"

# Ensure Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Start it and try again."
    exit 1
fi

# Build images first
if [ "$FORCE_BUILD" = true ]; then
    print_status "Building Docker images..."
    docker compose --env-file .env -f "$COMPOSE_FILE" build
    print_success "Build complete!"
fi

# Stop existing containers
print_status "Stopping existing containers..."
docker compose --env-file .env -f "$COMPOSE_FILE" down

# Start containers
print_status "Starting containers..."
docker compose --env-file .env -f "$COMPOSE_FILE" up $DETACHED

if [ -n "$DETACHED" ]; then
    print_success "Application started in detached mode!"
    print_status "Visit: http://localhost:3002"
    print_status "To view logs: docker compose -f $COMPOSE_FILE logs -f"
    print_status "To stop: docker compose -f $COMPOSE_FILE down"
else
    print_success "Application running. Press Ctrl+C to stop."
fi
