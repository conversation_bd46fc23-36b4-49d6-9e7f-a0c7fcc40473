# This file is automatically loaded by docker-compose for development overrides
version: '3.8'

services:
  cs-ai-app:
    # Override for development - use volume mounts for hot reloading
    build:
      target: base  # Use base stage for development
    command: npm run dev
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
    ports:
      - "3002:3000"
    # Remove healthcheck for development to avoid unnecessary overhead
    healthcheck:
      disable: true
