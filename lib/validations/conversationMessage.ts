import { z } from "zod"

export const ConversationMessageCreateSchema = z.object({
  conversationId: z.string().min(1, "Conversation ID is required"),
  content: z.string().min(1, "Message content is required"),
  category: z.enum(["CONVERSATION", "LOG"]).optional().default("CONVERSATION"),
})

export const ConversationMessageUpdateSchema = z.object({
  content: z.string().min(1, "Message content is required").optional(),
  updatedBy: z.string().min(1, "Updated by is required").optional(),
  updatedAt: z.date().optional(),
})

export const ConversationMessageIdSchema = z.object({
  id: z.string().min(1, "Message ID is required"),
})

export type ConversationMessageCreateInput = z.infer<
  typeof ConversationMessageCreateSchema
>
export type ConversationMessageUpdateInput = z.infer<
  typeof ConversationMessageUpdateSchema
>
export type ConversationMessageIdInput = z.infer<
  typeof ConversationMessageIdSchema
>
