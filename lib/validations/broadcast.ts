import { z } from "zod"
import {
  BroadcastStatus,
  BroadcastRecipientStatus,
} from "@/lib/repositories/broadcast/interface"

// Shared reusable schemas
export const IdSchema = z.string().min(1, "ID is required")
export const DateSchema = z.date()

// Base schemas
export const BroadcastStatusSchema = z.nativeEnum(BroadcastStatus)
export const BroadcastRecipientStatusSchema = z.nativeEnum(
  BroadcastRecipientStatus,
)

// Broadcast schemas
export const BroadcastCreateSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(200, "Title must be less than 200 characters"),
  message: z
    .string()
    .min(1, "Message is required")
    .max(1000, "Message must be less than 1000 characters"),
  manualSelectedTargetContacts: z.array(IdSchema).optional(), // Made optional for tag-based selection
  scheduledAt: DateSchema.optional(),
  deviceId: z.string().min(1, "Device ID is required"),

  recipientTags: z.array(z.string()).optional(),
  excludedRecipientIds: z.array(IdSchema).optional(),
  recipientSelectionType: z.enum(["manual", "tags"]).default("manual"),
})

export const BroadcastUpdateSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(200, "Title must be less than 200 characters")
    .optional(),
  message: z
    .string()
    .min(1, "Message is required")
    .max(1000, "Message must be less than 1000 characters")
    .optional(),
  manualSelectedTargetContacts: z
    .array(IdSchema)
    .min(1, "At least one contact is required")
    .optional(),
  scheduledAt: DateSchema.optional(),
  status: BroadcastStatusSchema.optional(),

  recipientTags: z.array(z.string()).optional(),
  excludedRecipientIds: z.array(IdSchema).optional(),
  recipientSelectionType: z.enum(["manual", "tags"]).optional(),
})

export const BroadcastQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  includeDeleted: z.boolean().default(false),
  status: BroadcastStatusSchema.optional(),
  createdBy: IdSchema.optional(),
  dateFrom: DateSchema.optional(),
  dateTo: DateSchema.optional(),
  sortBy: z.string().default("createdAt"),
  sortOrder: z.enum(["asc", "DESC"]).default("DESC"),
})

// Broadcast Recipient schemas
export const BroadcastRecipientCreateSchema = z.object({
  broadcastId: IdSchema,
  contactId: IdSchema,
  contactName: z.string().min(1, "Contact name is required"),
  contactPhone: z.string().min(1, "Contact phone is required"),
})

export const BroadcastRecipientUpdateSchema = z.object({
  status: BroadcastRecipientStatusSchema.optional(),
  sentAt: DateSchema.optional(),
  deliveredAt: DateSchema.optional(),
  failedAt: DateSchema.optional(),
  errorMessage: z.string().optional(),
})

export const BroadcastRecipientQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  broadcastId: IdSchema.optional(),
  contactId: IdSchema.optional(),
  status: BroadcastRecipientStatusSchema.optional(),
  sortBy: z.string().default("createdAt"),
  sortOrder: z.enum(["asc", "DESC"]).default("DESC"),
})

// Broadcast Operations schemas
export const BroadcastStartSchema = z.object({
  id: IdSchema,
})

export const BroadcastCancelSchema = z.object({
  id: IdSchema,
})

export const BroadcastScheduleSchema = z.object({
  id: IdSchema,
  scheduledAt: DateSchema,
})

// Bulk operation schemas
export const BroadcastBulkCreateSchema = z.object({
  items: z
    .array(BroadcastCreateSchema)
    .min(1, "At least one broadcast is required")
    .max(50, "Maximum 50 broadcasts allowed"),
})

export const BroadcastBulkUpdateSchema = z.object({
  updates: z
    .array(
      z.object({
        id: IdSchema,
        data: BroadcastUpdateSchema,
      }),
    )
    .min(1, "At least one update is required")
    .max(50, "Maximum 50 updates allowed"),
})

export const BroadcastBulkDeleteSchema = z.object({
  ids: z
    .array(IdSchema)
    .min(1, "At least one ID is required")
    .max(50, "Maximum 50 IDs allowed"),
  hardDelete: z.boolean().default(false),
})

// Contact selection schema (for the broadcast form)
export const ContactSelectionSchema = z.object({
  id: IdSchema,
  name: z.string(),
  phone: z.string(),
  email: z.string().optional(),
})

export const BroadcastFormSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(200, "Title must be less than 200 characters"),
  message: z
    .string()
    .min(1, "Message is required")
    .max(1000, "Message must be less than 1000 characters"),
  selectedContacts: z
    .array(ContactSelectionSchema)
    .min(1, "At least one contact must be selected"),
  scheduledAt: DateSchema.optional(),
})

// Export types
export type BroadcastCreateInput = z.infer<typeof BroadcastCreateSchema>
export type BroadcastUpdateInput = z.infer<typeof BroadcastUpdateSchema>
export type BroadcastQueryInput = z.infer<typeof BroadcastQuerySchema>
export type BroadcastRecipientCreateInput = z.infer<
  typeof BroadcastRecipientCreateSchema
>
export type BroadcastRecipientUpdateInput = z.infer<
  typeof BroadcastRecipientUpdateSchema
>
export type BroadcastRecipientQueryInput = z.infer<
  typeof BroadcastRecipientQuerySchema
>
export type BroadcastFormInput = z.infer<typeof BroadcastFormSchema>
export type ContactSelection = z.infer<typeof ContactSelectionSchema>
