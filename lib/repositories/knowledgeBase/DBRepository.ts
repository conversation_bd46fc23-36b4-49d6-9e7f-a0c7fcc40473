import {
  KnowledgeBaseEntry,
  KnowledgeBaseCreateInput,
  KnowledgeBaseUpdateInput,
  KnowledgeBaseQueryParams,
  KnowledgeBaseDocumentQueryParams,
  KnowledgeBaseParsedCreateInput,
  KnowledgeBaseParsedEntry,
} from "./interface"
import { BaseDbRepository, IDbCreate, IDbDelete, IDbGetById } from "../BaseDBRepository"

export type KnowledgeBaseDbQueryParams = KnowledgeBaseQueryParams
export type KnowledgeBaseDocumentDbQueryParams =
  KnowledgeBaseDocumentQueryParams

export interface KnowledgeBaseDBRepository
  extends BaseDbRepository<
    KnowledgeBaseEntry,
    KnowledgeBaseCreateInput & { createdBy: string; organizationId?: string },
    KnowledgeBaseUpdateInput,
    KnowledgeBaseDbQueryParams
  > {
  getCount(params: KnowledgeBaseQueryParams): Promise<{ total: number }>
  searchByContent(query: string, limit?: number): Promise<KnowledgeBaseEntry[]>
}

export interface KnowledgeBaseParsedDBRepository
  extends
  IDbGetById<KnowledgeBaseParsedEntry>,
  IDb<PERSON>reate<
    KnowledgeBaseParsedEntry,
    KnowledgeBaseParsedCreateInput & { createdBy: string; organizationId?: string }
  >,
  IDbDelete { }