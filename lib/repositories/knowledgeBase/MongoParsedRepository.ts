import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { MongoDriver } from "../MongoDriver"
import {
    KnowledgeBaseParsedDBRepository
} from "./DBRepository"
import {
    KnowledgeBaseEntry,
    KnowledgeBaseParsedCreateInput,
    KnowledgeBaseParsedEntry
} from "./interface"
import { generateId } from "@/lib/utils/common"

function mapMongoDocToKnowledgeBaseEntry(
    doc: any,
): KnowledgeBaseParsedEntry | null {
    if (!doc) return null
    const { _id, ...rest } = doc
    return {
        ...rest
    }
}

export class MongoKnowledgeBaseParsedRepository implements KnowledgeBaseParsedDBRepository {
    private collection

    constructor(driver: MongoDriver) {
        this.collection = driver.getCollection(MONGO_COLLECTIONS.KNOWLEDGE_BASE_PARSED)
        this.ensureIndexes()
    }
    async getById(id: string, includeDeleted?: boolean): Promise<KnowledgeBaseParsedEntry | null> {
        const doc = await this.collection.findOne({ id })
        return mapMongoDocToKnowledgeBaseEntry(doc)
    }
    async restore(id: string): Promise<boolean> {
        const result = await this.collection.updateOne(
            { id },
            {
                $unset: { deletedAt: "" },
                $set: { updatedAt: new Date() },
            },
        )
        return result.modifiedCount > 0
    }
    async create(data: KnowledgeBaseParsedCreateInput & { createdBy: string; organizationId?: string }): Promise<KnowledgeBaseParsedEntry> {
        const now = new Date()
        const doc = {
            id: generateId(),
            ...data,
            createdAt: now,
            updatedAt: now,
        }
        await this.collection.insertOne(doc)
        return mapMongoDocToKnowledgeBaseEntry(doc)!
    }
    async delete(id: string, hardDelete?: boolean): Promise<boolean> {
        const result = await this.collection.deleteOne({ id })
        return result.deletedCount > 0
    }

    private async ensureIndexes() {
        await this.collection.createIndexes([
            { key: { id: 1 }, unique: true },
            { key: { organizationId: 1 } },
            { key: { isActive: 1 } },
            { key: { createdAt: -1 } },
        ])
    }
}