import { KnowledgeBaseParserService } from "./interface"
import { ERROR_CODES } from "@/app/api/error_codes"

const PARSER_ENGINE_URL = process.env.PARSER_ENGINE_URL!
const PARSER_CALLBACK_URL = process.env.PARSER_CALLBACK_URL!
const INTERNAL_SECRET_TOKEN = process.env.INTERNAL_SECRET_TOKEN!

export class KnowledgebaseParserService implements KnowledgeBaseParserService {
    async parseKnowledgeBaseContent(
        knowledgeBaseId: string,
        content: string,
        context: "KNOWLEDGEBASES" | "AIRULES" | "DATASOURCES" | "MESSAGETEMPLATES" = "KNOWLEDGEBASES"
    ): Promise<{
        status: "success" | "failed"
        errors?: string[]
        errorCodes?: string[]
    }> {
        try {
            const payload = {
                knowledgeBaseId,
                content,
                context,
            }

            const callbackUrl = `${PARSER_CALLBACK_URL}?context=${context}`

            const res = await fetch(PARSER_ENGINE_URL, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-Parser-Webhook-Endpoint": callbackUrl,
                    "X-Internal-System-Token": INTERNAL_SECRET_TOKEN,
                },
                body: JSON.stringify(payload),
            })

            if (!res.ok) {
                const text = await res.text()
                return {
                    status: "failed",
                    errors: [`Parser webhook error: ${res.status} - ${text}`],
                    errorCodes: [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
                }
            }

            return { status: "success" }
        } catch (error: any) {
            return {
                status: "failed",
                errors: [error.message || "Unknown error sending to parser webhook"],
                errorCodes: [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
            }
        }
    }
}
