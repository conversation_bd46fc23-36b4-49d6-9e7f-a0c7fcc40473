import { SessionContext } from "../auth/types"

export interface Datasource {
  id: string
  name: string
  type: string
  url?: string // Optional for TEXT type
  content?: string // For TEXT type datasources
  accessKey?: string
  isActive?: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
  // Relationship field to track source knowledge base entry
  sourceKnowledgeBaseId?: string // ID of the knowledge base entry that generated this datasource
}

export interface DatasourceCreateInput {
  name: string
  type: string
  url?: string // Optional for TEXT type
  content?: string // For TEXT type datasources
  accessKey?: string
  isActive?: boolean
  createdBy: string
  organizationId?: string
  sourceKnowledgeBaseId?: string // ID of the knowledge base entry that generated this datasource
}

export interface DatasourceUpdateInput {
  name?: string
  type?: string
  url?: string
  content?: string // For TEXT type datasources
  accessKey?: string
  isActive?: boolean
  updatedBy?: string
  organizationId?: string
}

export type DatasourceQueryParams = {
  search?: string
  limit?: number
  offset?: number
  sort?: { field: keyof Datasource | string; direction: "ASC" | "DESC" }[]
  filters?: {
    field: keyof Datasource | string
    value: Datasource[keyof Datasource] | any
  }[]
  includeDeleted?: boolean
}

export interface DatasourceBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<Datasource | null>
  getAll(
    params: DatasourceQueryParams,
    context: SessionContext,
  ): Promise<{
    items: Datasource[]
    total: number
  }>
  create(
    data: DatasourceCreateInput,
    context: SessionContext,
  ): Promise<Datasource>
  update(
    id: string,
    data: DatasourceUpdateInput,
    context: SessionContext,
  ): Promise<Datasource | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>

  searchMatchingQuery(
    query: string,
    context: SessionContext,
    limit: number,
  ): Promise<Datasource[]>
}
