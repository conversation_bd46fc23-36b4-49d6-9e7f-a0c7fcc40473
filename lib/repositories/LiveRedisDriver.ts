import { Redis } from "@upstash/redis"
import { InMemoryDriver } from "./RedisDriver"

// Global cached instance (for serverless efficiency)
let cachedRedis: Redis | null = null

export class LiveRedisDriver implements InMemoryDriver {
  private redis: Redis

  constructor() {
    if (!cachedRedis) {
      if (
        !process.env.UPSTASH_REDIS_REST_URL ||
        !process.env.UPSTASH_REDIS_REST_TOKEN
      ) {
        throw new Error("Missing Upstash Redis environment variables.")
      }

      cachedRedis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL!,
        token: process.env.UPSTASH_REDIS_REST_TOKEN!,
      })
    }

    this.redis = cachedRedis
  }

  keys(pattern: string): Promise<string[]> {
    return this.redis.keys(pattern)
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    await this.redis.set(key, value)
  }

  async get<T>(key: string): Promise<T | null> {
    return await this.redis.get<T>(key)
  }

  async close(): Promise<void> {
    // Not required for Upstash (no TCP), but included for interface parity
  }
}

export const driver = new LiveRedisDriver()
