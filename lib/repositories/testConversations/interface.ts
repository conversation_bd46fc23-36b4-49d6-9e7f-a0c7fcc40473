import { SessionContext } from "../auth/types"

export interface TestConversation {
  id: string
  customerName: string
  customerPhone: string
  scenario: TestScenario
  messages: TestMessage[]
  status: 'OPEN' | 'CLOSED'
  isAiEnabled: boolean
  createdAt: string
  updatedAt: string
  scenarioDescription: string
  sampleMessages: string[]
  title: string
}

export interface TestConversationCreateInput {
  customerName: string
  customerPhone: string
  scenario: TestScenario
  messages: TestMessage[]
  status: 'OPEN' | 'CLOSED'
  isAiEnabled: boolean
  scenarioDescription: string
  sampleMessages: string[]
  title: string
}

export interface TestConversationUpdateInput {
  customerName?: string
  customerPhone?: string
  scenario?: TestScenario
  messages?: TestMessage[]
  status?: 'OPEN' | 'CLOSED'
  isAiEnabled?: boolean
  scenarioDescription?: string
  sampleMessages?: string[]
  title?: string
}

export type TestScenario =
  | 'general_inquiry'
  | 'product_support'
  | 'billing_issue'
  | 'technical_problem'
  | 'complaint'
  | 'compliment'
  | 'order_status'
  | 'refund_request'
  | 'custom'

export interface TestMessage {
  id: string
  content: string
  senderId: string
  senderName: string
  isFromCustomer: boolean
  isFromAI: boolean
  timestamp: string
  messageType: 'TEXT' | 'IMAGE' | 'LOG'
  metadata?: {
    wahaMessageId?: string
    isTestMessage?: boolean
  }
}

export interface TestConversationQueryParams {
  search?: string
  filters?: { field: keyof TestConversation | string; value: any }[]
  sort?: { field: keyof TestConversation | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface TestConversationBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<TestConversation | null>
  getAll(
    params: TestConversationQueryParams,
    context: SessionContext,
  ): Promise<{
    items: TestConversation[]
    total: number
  }>
  create(
    data: TestConversationCreateInput,
    context: SessionContext,
  ): Promise<TestConversation>
  update(
    id: string,
    data: TestConversationUpdateInput,
    context: SessionContext,
  ): Promise<TestConversation | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>
}
