import {
  TestConversation,
  TestConversationCreateInput,
  TestConversationUpdateInput,
  TestConversationQueryParams,
} from "./interface"
import { ObjectId, WithId, Document } from "mongodb"
import { MongoDriver } from "../MongoDriver"
import {
  TestConversationDBRepository,
  TestConversationDbQueryParams,
} from "./DBRepository"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { buildMongoQuery } from "../queryBuilder"

function mapMongoDocToTestConversation(
  doc: WithId<Document> | null,
): TestConversation | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as TestConversation
}

export class MongoTestConversationRepository implements TestConversationDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.TEST_CONVERSATIONS)
  }

  async getById(
    id: string,
    includeDeleted = false,
  ): Promise<TestConversation | null> {
    const query: any = { id: id }
    if (!includeDeleted) query.deletedAt = { $exists: false }
    const doc = await this.collection.findOne(query)
    return mapMongoDocToTestConversation(doc)
  }

  async getAll(
    params: TestConversationQueryParams,
  ): Promise<{ items: TestConversation[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      params,
      ["name", "description", "participants", "tags", "lastMessageAt", "updatedAt"],
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)
    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToTestConversation)
      .filter((i): i is TestConversation => i !== null)

    const total = await this.collection.countDocuments(query)
    return { items, total }
  }

  async getCount(params: TestConversationQueryParams): Promise<{ total: number }> {
    const { query } = buildMongoQuery({ query: {}, sort: {} }, params, [
      "name",
      "description",
      "participants",
      "tags",
      "lastMessageAt",
      "updatedAt",
    ])
    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: TestConversationCreateInput): Promise<TestConversation> {
    const now = new Date()
    console.log("testConversation", data)
    const doc = {
      ...data,
      createdAt: now,
      updatedAt: now,
    }
    const result = await this.collection.insertOne(doc)
    return mapMongoDocToTestConversation({
      _id: result.insertedId,
      ...doc,
    } as WithId<Document>)!
  }

  async update(
    id: string,
    data: TestConversationUpdateInput,
  ): Promise<TestConversation | null> {
    // First check if the document exists
    const existing = await this.collection.findOne({
      id: id,
      deletedAt: { $exists: false },
    })

    if (!existing) return null

    // Filter out undefined values to avoid overwriting existing fields
    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() }).filter(
        ([_, value]) => value !== undefined,
      ),
    )

    // Update the document
    const updateResult = await this.collection.updateOne(
      { id: id, deletedAt: { $exists: false } },
      { $set: updateData },
    )

    if (updateResult.modifiedCount === 0) return null

    // Fetch the updated document
    const updated = await this.collection.findOne({
      id: id,
    })

    if (!updated) return null
    return mapMongoDocToTestConversation(updated)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id: id })
      return result.deletedCount === 1
    } else {
      const result = await this.collection.updateOne(
        { id: id },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount === 1
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id: id },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() },
      },
    )
    return result.modifiedCount === 1
  }
  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
