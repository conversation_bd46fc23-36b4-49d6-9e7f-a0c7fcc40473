import {
  TestConversation,
  TestConversationCreateInput,
  TestConversationUpdateInput,
} from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type TestConversationDbQueryParams = BaseQueryParams<TestConversation>

export interface TestConversationDBRepository
  extends BaseDbRepository<
    TestConversation,
    TestConversationCreateInput,
    TestConversationUpdateInput,
    TestConversationDbQueryParams
  > { }
