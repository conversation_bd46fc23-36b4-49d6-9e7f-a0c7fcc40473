import {
  Conversation,
  ConversationCreateInput,
  ConversationUpdateInput,
  ConversationQueryParams,
  ConversationBusinessLogicInterface,
} from "./interface"
import { createError, generateId } from "@/lib/utils/common"
import { ConversationDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"
import { driver } from "../LiveRedisDriver"

function mapDocToConversation(doc: any): Conversation | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as Conversation
}

export class ConversationBusinessLogic
  implements ConversationBusinessLogicInterface {
  constructor(private readonly db: ConversationDBRepository) { }

  private validateId(id: string) {
    if (!id || !id.trim())
      throw createError("Conversation ID is required", "INVALID_ID")
  }

  private trimCreateInput(
    data: ConversationCreateInput,
  ): ConversationCreateInput {
    return {
      ...data,
      name: data.name.trim(),
      description: data.description?.trim(),
      participants: data.participants?.map((p) => p.trim()) ?? [],
      tags: data.tags?.map((t) => t.trim()) ?? [],
      isActive: data.isActive ?? true,
      status: data.status ?? "OPEN", // Default to OPEN if not specified
      previousConversationId: data.previousConversationId?.trim(),
    }
  }

  private trimUpdateInput(
    data: ConversationUpdateInput,
  ): ConversationUpdateInput {
    return {
      ...data,
      name: data.name?.trim(),
      description: data.description?.trim(),
      participants: data.participants?.map((p) => p.trim()),
      lastMessage: data.lastMessage && {
        body: data.lastMessage.body?.trim() || "",
        fromMe: data.lastMessage.fromMe,
        _data: data.lastMessage._data,
        ack: data.lastMessage.ack,
      },
      tags: data.tags?.map((t) => t.trim()),
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }
    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<Conversation | null> {
    this.validateId(id)
    const contextFilters = this.buildContextFilters(context)
    const result = await this.db.getById(id, includeDeleted)

    return mapDocToConversation(result)
  }



  async getAll(
    params: ConversationQueryParams,
    context: SessionContext,
  ): Promise<{ items: Conversation[]; total: number }> {
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      offset: params?.page,
      filters: [...(params.filters || []), ...contextFilters],
    }

    const { items, total } = await this.db.getAll(paramsWithContext)
    return {
      items: items
        .map(mapDocToConversation)
        .filter((i): i is Conversation => i !== null),
      total,
    }
  }

  async create(
    data: ConversationCreateInput,
    context: SessionContext,
  ): Promise<Conversation> {
    const trimmedData = this.trimCreateInput(data)
    const dataWithContext = {
      ...trimmedData,
      id: generateId("conversationroom"),
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    const created = await this.db.create(dataWithContext)
    return mapDocToConversation(created)!
  }

  async update(
    id: string,
    data: ConversationUpdateInput,
    context: SessionContext,
  ): Promise<Conversation | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Conversation not found", "NOT_FOUND")
    }

    const existingConversation = existingResult.items[0]

    if (data.name && data.name.trim() !== existingConversation.name) {
      const duplicates = await this.db.getAll({
        filters: [
          { field: "name", value: data.name.trim() },
          ...contextFilters,
        ],
      })
      if (duplicates.items.some((c) => c.id !== id)) {
        throw createError(
          "Another Conversation with this name exists",
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = {
      ...this.trimUpdateInput(data),
      updatedBy: context.user.id,
    }

    if (data.isAiEnabled !== undefined) {
      if (data.isAiEnabled) {
        trimmedData.aiDisabledAt = null
        trimmedData.aiDisabledBy = null
      } else {
        trimmedData.aiDisabledAt = new Date()
        trimmedData.aiDisabledBy = context.user.id
      }
    }

    const updated = await this.db.update(id, trimmedData)
    return updated ? mapDocToConversation(updated) : null
  }

  async updateAiAnsweringState(
    id: string,
    isAiAnswering: boolean,
    context: SessionContext,
  ): Promise<Conversation | null> {
    this.validateId(id)

    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Conversation not found", "NOT_FOUND")
    }

    const trimmedData = {
      isAiAnswering,
      updatedBy: context.user.id,
    }

    const updated = await this.db.update(id, trimmedData)
    return updated ? mapDocToConversation(updated) : null
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)
    const contextFilters = this.buildContextFilters(context)

    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Conversation not found", "NOT_FOUND")
    }

    return this.db.delete(id, hardDelete)
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)
    const contextFilters = this.buildContextFilters(context)

    const conversationResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted: true,
    })

    if (conversationResult.items.length === 0) return false
    const conversation = conversationResult.items[0]
    if (!conversation.deletedAt) return false

    const conflict = await this.db.getCount({
      filters: [{ field: "name", value: conversation.name }, ...contextFilters],
    })

    if (conflict.total > 0) return false

    return this.db.restore(id)
  }
}
