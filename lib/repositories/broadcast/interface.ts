import { BaseQueryParams } from "../BaseDBRepository"
import { SessionContext } from "../auth/types"

export interface Broadcast {
  id: string
  title: string
  message: string
  manualSelectedTargetContacts: string[] // Array of contact IDs (for backward compatibility)
  recipients: BroadcastRecipient[] // Array of recipient status
  deviceId: string // Device to use for sending
  status: BroadcastStatus
  totalTargets: number
  sentCount: number
  failedCount: number
  pendingCount: number
  scheduledAt?: Date
  startedAt?: Date
  completedAt?: Date
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string

  // New fields for tag-based broadcasting
  recipientTags?: string[] // Array of tags to include
  excludedRecipientIds?: string[] // Array of contact IDs to exclude
  recipientSelectionType?: "manual" | "tags" // How recipients were selected
}

export interface BroadcastRecipient {
  contactId: string
  name: string
  phone?: string
  email?: string
  status: BroadcastRecipientStatus
  sentAt?: Date
  deliveredAt?: Date
  failedAt?: Date
  errorMessage?: string
}

export interface BroadcastStats {
  totalBroadcasts: number
  activeBroadcasts: number
  completedBroadcasts: number
  totalMessagesSent: number
  averageSuccessRate: number
  recentActivity: BroadcastActivity[]
}

export interface BroadcastActivity {
  id: string
  broadcastId: string
  broadcastTitle: string
  action: string
  timestamp: Date
  details?: string
}

// Enums
export enum BroadcastStatus {
  DRAFT = "draft",
  SCHEDULED = "scheduled",
  SENDING = "sending",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
}

export enum BroadcastRecipientStatus {
  PENDING = "pending",
  SENT = "sent",
  DELIVERED = "delivered",
  FAILED = "failed",
}

// Input Types
export interface BroadcastCreateInput {
  title: string
  message: string
  manualSelectedTargetContacts: string[] // For backward compatibility
  deviceId: string
  scheduledAt?: Date

  // New fields for tag-based broadcasting
  recipientTags?: string[] // Array of tags to include
  excludedRecipientIds?: string[] // Array of contact IDs to exclude
  recipientSelectionType?: "manual" | "tags" // How recipients were selected
}

export interface BroadcastUpdateInput {
  title?: string
  message?: string
  manualSelectedTargetContacts?: string[]
  recipients?: BroadcastRecipient[]
  deviceId?: string
  scheduledAt?: Date
  status?: BroadcastStatus
  totalTargets?: number
  sentCount?: number
  failedCount?: number
  pendingCount?: number
  startedAt?: Date
  completedAt?: Date
  updatedBy?: string

  // New fields for tag-based broadcasting
  recipientTags?: string[]
  excludedRecipientIds?: string[]
  recipientSelectionType?: "manual" | "tags"
}

// Query Types
export interface BroadcastQueryParams extends BaseQueryParams<Broadcast> {
  status?: BroadcastStatus
  createdBy?: string
  dateFrom?: Date
  dateTo?: Date
}

// Business Logic Interface
export interface BroadcastBusinessLogicInterface {
  // Broadcast CRUD
  create(
    data: BroadcastCreateInput,
    context: SessionContext,
  ): Promise<Broadcast>
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<Broadcast | null>
  getAll(
    params: BroadcastQueryParams,
    context: SessionContext,
  ): Promise<{ items: Broadcast[]; total: number }>
  update(
    id: string,
    data: BroadcastUpdateInput,
    context: SessionContext,
  ): Promise<Broadcast | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  duplicate(
    id: string,
    context: SessionContext,
    newTitle?: string,
  ): Promise<Broadcast>

  // Broadcast Operations
  startBroadcast(id: string, context: SessionContext): Promise<boolean>
  cancelBroadcast(id: string, context: SessionContext): Promise<boolean>
  scheduleBroadcast(
    id: string,
    scheduledAt: Date,
    context: SessionContext,
  ): Promise<boolean>

  // Analytics
  getStats(context: SessionContext): Promise<BroadcastStats>
  getBroadcastProgress(
    id: string,
    context: SessionContext,
  ): Promise<{
    totalTargets: number
    sentCount: number
    failedCount: number
    pendingCount: number
    successRate: number
    status: BroadcastStatus
  }>
  getRecipients(
    id: string,
    context: SessionContext,
  ): Promise<BroadcastRecipient[]>
}
