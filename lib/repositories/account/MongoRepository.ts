import { AccountDBRepositoryInterface, AccountInfo } from "."
import { SessionContext } from "../auth/types"
import { UserBusinessLogicInterface } from "../users"

export class AccountDBRepository implements AccountDBRepositoryInterface {
  constructor(private userBusinessLogic: UserBusinessLogicInterface) { }

  async getAccountById(userId: string, context: SessionContext): Promise<AccountInfo | null> {
    const user = await this.userBusinessLogic.getById(userId, context)

    if (!user) {
      return null
    }

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  }

  async updateAccountName(userId: string, name: string, context: SessionContext): Promise<boolean> {
    const currentUser = await this.userBusinessLogic.getById(userId, context)

    if (!currentUser) {
      return false
    }

    const updateData = {
      name,
      email: currentUser.email,
      tags: currentUser.tags,
      isActive: currentUser.isActive,
      updatedBy: userId,
    }

    const updatedUser = await this.userBusinessLogic.update(userId, updateData, context)

    return updatedUser !== null
  }
}
