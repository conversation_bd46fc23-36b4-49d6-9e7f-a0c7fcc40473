import { SessionContext } from "../auth/types"

type ConversationMessageType = "TEXT" | "IMAGE" | "FILE" | "AUDIO" | "VIDEO"
type ConversationMessageCategory = "CONVERSATION" | "LOG"

export interface ConversationMessage {
  id: string
  conversationId: string
  content: string
  messageType: ConversationMessageType
  category?: ConversationMessageCategory
  senderId: string
  metadata?: Record<string, any>
  isRead?: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
}

export interface ConversationMessageCreateInput {
  conversationId: string
  content: string
  messageType: string
  category?: ConversationMessageCategory
  senderId: string
  metadata?: Record<string, any>
}

export interface ConversationMessageUpdateInput {
  content?: string
  messageType?: string
  sender?: string
  recipient?: string
  metadata?: Record<string, any>
  isRead?: boolean
  updatedBy?: string
  organizationId?: string
}

export interface ConversationMessageQueryParams {
  search?: string
  filters?: { field: keyof ConversationMessage | string; value: any }[]
  sort?: {
    field: keyof ConversationMessage | string
    direction: "ASC" | "DESC"
  }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface ConversationMessageBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<ConversationMessage | null>
  getAll(
    conversationId: string,
    params: ConversationMessageQueryParams,
    context: SessionContext,
  ): Promise<{
    items: ConversationMessage[]
    total: number
  }>
  create(
    data: ConversationMessageCreateInput,
    context: SessionContext,
  ): Promise<ConversationMessage>
  update(
    id: string,
    data: ConversationMessageUpdateInput,
    context: SessionContext,
  ): Promise<ConversationMessage | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>
  bulkCreate(
    data: ConversationMessageCreateInput[],
    context: SessionContext,
  ): Promise<ConversationMessage[]>
  bulkUpdate(
    updates: { id: string; data: ConversationMessageUpdateInput }[],
    context: SessionContext,
  ): Promise<number>
  bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<number>
}
