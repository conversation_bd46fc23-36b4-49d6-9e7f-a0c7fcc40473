import { BaseVectorDBRepository } from "../BaseVectorDBRepository"
import { Pinecone, Index, RecordMetadata } from "@pinecone-database/pinecone"
import { SessionContext } from "../auth/types"

export interface AiRuleVectorDB {
  id: string
  message: string
  rule: string
}

export interface AiRuleVectorDBRepository {
  create(item: AiRuleVectorDB, context: SessionContext): Promise<void>
  update(id: string, item: AiRuleVectorDB, context: SessionContext): Promise<void>
  delete(id: string, context: SessionContext): Promise<void>
  search(query: string, topK: number, context: SessionContext): Promise<AiRuleVectorDB[]>
}

export class PineconeAiRuleVectorDBRepository
  implements AiRuleVectorDBRepository {
  private pinecone: Pinecone

  constructor(pineconeApiKey: string) {
    this.pinecone = new Pinecone({ apiKey: pineconeApiKey })
  }

  private getNamespace(context: SessionContext): Index<RecordMetadata> {
    return this.pinecone.index("message-rule").namespace(context.user.id)
  }

  async create(item: AiRuleVectorDB, context: SessionContext): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.upsertRecords([
      {
        _id: item.id,
        text: item.message,
        rule: item.rule,
      },
    ])
  }

  async update(id: string, item: AiRuleVectorDB, context: SessionContext): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.upsertRecords([
      {
        _id: id,
        text: item.message,
        rule: item.rule,
      },
    ])
  }

  async delete(id: string, context: SessionContext): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.deleteMany([id])
  }

  async search(query: string, topK: number = 3, context: SessionContext): Promise<AiRuleVectorDB[]> {
    const namespace = this.getNamespace(context)
    const searchResult = await namespace.searchRecords({
      query: {
        topK,
        inputs: { text: query }, // this gets embedded automatically
      },
    })

    return (searchResult.result.hits || []).map((hit) => {
      const metadata = hit.fields as any

      return {
        id: hit._id,
        message: metadata?.text || "",
        rule: metadata?.rule || "",
      }
    })
  }
}
