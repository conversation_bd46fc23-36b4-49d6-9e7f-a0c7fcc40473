import {
  <PERSON>Rule,
  AiRuleCreateInput,
  AiRuleUpdateInput,
  AiRuleQueryParams,
  AiRuleBusinessLogicInterface,
} from "./interface"
import { createError } from "@/lib/utils/common"
import { AiRuleDBRepository } from "./DBRepository"
import { AiRuleVectorDBRepository, AiRuleVectorDB } from "./VectorDBRepository"
import { SessionContext } from "../auth/types"

export class AiRuleBusinessLogic implements AiRuleBusinessLogicInterface {
  constructor(
    private readonly db: AiRuleDBRepository,
    private readonly vectordb: AiRuleVectorDBRepository,
  ) { }

  private validateId(id: string) {
    if (!id || !id.trim())
      throw createError("AiRule ID is required", "INVALID_ID")
  }

  private trimCreateInput(data: AiRuleCreateInput): AiRuleCreateInput {
    return {
      ...data,
      name: data.name.trim(),
      description: data.description?.trim() ?? "",
      tags: data.tags?.map((t) => t.trim()) ?? [],
      conditions: data.conditions?.map((c) => c.trim()) ?? [],
      actions: data.actions?.map((a) => a.trim()) ?? [],
      isActive: data.isActive ?? true,
    }
  }

  private trimUpdateInput(data: AiRuleUpdateInput): AiRuleUpdateInput {
    return {
      ...data,
      name: data.name?.trim(),
      description: data.description?.trim(),
      tags: data.tags?.map((t) => t.trim()),
      conditions: data.conditions?.map((c) => c.trim()),
      actions: data.actions?.map((a) => a.trim()),
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<AiRule | null> {
    this.validateId(id)
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted,
    }
    const result = await this.db.getAll(paramsWithContext)
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    params: AiRuleQueryParams,
    context: SessionContext,
  ): Promise<{ items: AiRule[]; total: number }> {
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }
    return this.db.getAll(paramsWithContext)
  }

  async create(
    data: AiRuleCreateInput,
    context: SessionContext,
  ): Promise<AiRule> {
    const trimmedData = this.trimCreateInput(data)

    // Add context information to the data
    const dataWithContext = {
      ...trimmedData,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    // Check for duplicate name within the same context
    // const contextFilters = this.buildContextFilters(context)
    // const existing = await this.db.getCount({
    //   filters: [{ field: "name", value: trimmedData.name }, ...contextFilters],
    // })

    // if (existing.total > 0) {
    //   throw createError(
    //     "AI Rule with this name already exists",
    //     "DUPLICATE_NAME",
    //   )
    // }

    const result = await this.db.create(dataWithContext)

    await this.vectordb.create({
      id: result.id,
      message: result.conditions.join(","),
      rule: result.actions.join(","),
    }, context)

    return result
  }

  async update(
    id: string,
    data: AiRuleUpdateInput,
    context: SessionContext,
  ): Promise<AiRule | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    // Check if AI rule exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("AI Rule not found", "NOT_FOUND")
    }

    const existingRule = existingResult.items[0]

    if (data.name && data.name.trim() !== existingRule.name) {
      const duplicates = await this.db.getAll({
        filters: [
          { field: "name", value: data.name.trim() },
          ...contextFilters,
        ],
      })
      if (duplicates.items.some((r) => r.id !== id)) {
        throw createError(
          "Another AI Rule with this name exists",
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = {
      ...this.trimUpdateInput(data),
      updatedBy: context.user.id,
    }

    await this.vectordb.update(id, {
      id,
      message: trimmedData.conditions?.join(",") || "",
      rule: trimmedData.actions?.join(",") || "",
    }, context)

    return this.db.update(id, trimmedData)
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if AI rule exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("AI Rule not found", "NOT_FOUND")
    }

    await this.vectordb.delete(id, context)
    return this.db.delete(id, hardDelete)
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)

    // Check if AI rule exists and belongs to the current context (including deleted)
    const contextFilters = this.buildContextFilters(context)
    const ruleResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted: true,
    })

    if (ruleResult.items.length === 0) return false

    const rule = ruleResult.items[0]
    if (!rule.deletedAt) return false

    // Check for conflicts by name within the same context
    const conflict = await this.db.getCount({
      filters: [{ field: "name", value: rule.name }, ...contextFilters],
    })
    if (conflict.total > 0) return false

    await this.vectordb.create({
      id: rule.id,
      message: rule.conditions.join(","),
      rule: rule.actions.join(","),
    })

    return this.db.restore(id)
  }

  async bulkCreate(
    data: AiRuleCreateInput[],
    context: SessionContext,
  ): Promise<AiRule[]> {
    if (!Array.isArray(data) || data.length === 0) {
      throw createError(
        "Input must be a non-empty array",
        "INVALID_BULK_CREATE_DATA",
      )
    }

    const contextFilters = this.buildContextFilters(context)

    for (const entry of data) {
      this.trimCreateInput(entry)
      const existing = await this.db.getCount({
        filters: [
          { field: "name", value: entry.name.trim() },
          ...contextFilters,
        ],
      })
      if (existing.total > 0) {
        throw createError(
          `Duplicate name found: ${entry.name}`,
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = data.map((d) => ({
      ...this.trimCreateInput(d),
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }))
    const result = await this.db.bulkCreate(trimmedData)

    for (const item of result) {
      await this.vectordb.create({
        id: item.id,
        message: item.conditions.join(","),
        rule: item.actions.join(","),
      }, context)
    }

    return result
  }

  async bulkUpdate(
    updates: { id: string; data: AiRuleUpdateInput }[],
    context: SessionContext,
  ): Promise<number> {
    if (!Array.isArray(updates) || updates.length === 0) {
      throw createError(
        "Input must be a non-empty array",
        "INVALID_BULK_UPDATE_DATA",
      )
    }

    const contextFilters = this.buildContextFilters(context)

    for (const { id, data } of updates) {
      this.validateId(id)

      if (!data || Object.keys(data).length === 0) {
        throw createError(
          `No data provided for update of ID ${id}`,
          "INVALID_UPDATE_DATA",
        )
      }

      // Check if AI rule exists and belongs to the current context
      const existingResult = await this.db.getAll({
        filters: [{ field: "id", value: id }, ...contextFilters],
      })

      if (existingResult.items.length === 0) {
        throw createError(`AI Rule with ID ${id} not found`, "NOT_FOUND")
      }

      const existingRule = existingResult.items[0]

      if (data.name && data.name.trim() !== existingRule.name) {
        const duplicates = await this.db.getAll({
          filters: [
            { field: "name", value: data.name.trim() },
            ...contextFilters,
          ],
        })
        if (duplicates.items.some((r) => r.id !== id)) {
          throw createError(
            `Duplicate name in update: ${data.name}`,
            "DUPLICATE_NAME",
          )
        }
      }

      this.trimUpdateInput(data)
    }

    const trimmedUpdates = updates.map(({ id, data }) => ({
      id,
      data: {
        ...this.trimUpdateInput(data),
        updatedBy: context.user.id,
      },
    }))

    for (const { id, data } of trimmedUpdates) {
      await this.vectordb.update(id, {
        id,
        message: data.conditions?.join(",") || "",
        rule: data.actions?.join(",") || "",
      }, context)
    }

    return this.db.bulkUpdate(trimmedUpdates)
  }

  async bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete = false,
  ): Promise<number> {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError(
        "IDs must be a non-empty array",
        "INVALID_BULK_DELETE_DATA",
      )
    }

    const contextFilters = this.buildContextFilters(context)

    for (const id of ids) {
      this.validateId(id)

      // Check if AI rule exists and belongs to the current context
      const ruleResult = await this.db.getAll({
        filters: [{ field: "id", value: id }, ...contextFilters],
      })

      if (ruleResult.items.length === 0) {
        throw createError(`AI Rule with ID ${id} not found`, "NOT_FOUND")
      }

      await this.vectordb.delete(id, context)
    }

    return this.db.bulkDelete(ids, hardDelete)
  }

  async search(query: string): Promise<AiRule[]> {
    if (!query || !query.trim()) {
      return []
    }

    const searchTerm = query.trim()
    const result = await this.db.getAll({ search: searchTerm })

    return result.items
  }

  async queryVectorDB(
    query: string,
    context: SessionContext,
    topK: number = 3,
  ): Promise<AiRuleVectorDB[]> {
    if (!query || !query.trim()) {
      return []
    }

    const searchTerm = query.trim()
    const vectorResults = await this.vectordb.search(searchTerm, topK, context)

    // Filter vector results to only include rules that belong to the current context
    const contextFilters = this.buildContextFilters(context)
    const filteredResults = []

    for (const vectorResult of vectorResults) {
      const ruleResult = await this.db.getAll({
        filters: [{ field: "id", value: vectorResult.id }, ...contextFilters],
      })

      if (ruleResult.items.length > 0) {
        filteredResults.push(vectorResult)
      }
    }

    return filteredResults
  }
}
