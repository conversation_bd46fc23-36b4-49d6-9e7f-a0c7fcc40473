import { ConsoleEventSender } from "@/microservices/ConsoleEventSender"
import { ResendEmailSender } from "@/microservices/ResendEmailSender"
import { AiRuleBusinessLogic } from "./aiRules"
import { MongoAiRuleRepository } from "./aiRules/MongoRepository"
import { PineconeAiRuleVectorDBRepository } from "./aiRules/VectorDBRepository"
import { AiWorkflowExecutionBusinessLogic } from "./aiWorkflowExecutions"
import { MongoAiWorkflowExecutionRepository } from "./aiWorkflowExecutions/MongoRepository"
import { AuthBusinessLogic, MongoAuthDBRepository } from "./auth"
import { AuthDBRedisRepository } from "./auth/RedisInMemoryRepository"
import { BroadcastBusinessLogic } from "./broadcast/BusinessLogic"
import { MongoBroadcastRepository } from "./broadcast/MongoRepository"
import { DeviceBusinessLogicImpl } from "./devices/BusinessLogic"
import { MongoDeviceRepository } from "./devices/MongoRepository"
import { ContactBusinessLogic } from "./contacts"
import { MongoContactRepository } from "./contacts/MongoRepository"
import { ConversationMessageBusinessLogic } from "./conversationMessages"
import { MongoConversationMessageRepository } from "./conversationMessages/MongoRepository"
import { ConversationBusinessLogic } from "./conversations"
import { MongoConversationRepository } from "./conversations/MongoRepository"
import { CustomerProfileBusinessLogic } from "./customerProfiles"
import { MongoCustomerProfileRepository } from "./customerProfiles/MongoRepository"
import { DatasourceBusinessLogic } from "./datasources"
import { MongoDatasourceRepository } from "./datasources/MongoRepository"
import { KnowledgeBaseBusinessLogic } from "./knowledgeBase/BusinessLogic"
import { MongoKnowledgeBaseRepository } from "./knowledgeBase/MongoRepository"
import { driver } from "./LiveMongoDriver"
import { driver as inMemoryDriver } from "./LiveRedisDriver"
import { MessageTemplateBusinessLogic } from "./messageTemplates"
import { MongoMessageTemplateRepository } from "./messageTemplates/MongoRepository"
import { PineconeMessageTemplateVectorDBRepository } from "./messageTemplates/VectorDBRepository"
import { UserBusinessLogic } from "./users"
import { MongoUserRepository } from "./users/MongoRepository"
import { AccountBusinessLogicImpl } from "./account"
import { PineconeDatasourceVectorDBRepository } from "./datasources/VectorDBRepository"
import { KnowledgebaseParserService } from "./knowledgeBase/KnowledgeBaseParserService"
import { AccountDBRepository } from "./account/MongoRepository"
import { PineconeKnowledgeBaseParsedVectorDBRepository } from "./knowledgeBase/KnowledgeBaseParsedVectorDBRepository"
import { MongoKnowledgeBaseParsedRepository } from "./knowledgeBase/MongoParsedRepository"
import { TestConversationBusinessLogic } from "./testConversations"
import { MongoTestConversationRepository } from "./testConversations/MongoRepository"

const authDb = new MongoAuthDBRepository(driver)
const inMemoryAuthRepo = new AuthDBRedisRepository(inMemoryDriver)
const emailSender = new ResendEmailSender()
const eventSender = new ConsoleEventSender()
export const authBusinessLogic = new AuthBusinessLogic(
  authDb,
  inMemoryAuthRepo,
  emailSender,
  eventSender,
)

const aiRulesDb = new MongoAiRuleRepository(driver)
const aiRulesVectordb = new PineconeAiRuleVectorDBRepository(
  process.env.PINECONE_API_KEY!,
)
export const aiRulesBusinessLogic = new AiRuleBusinessLogic(
  aiRulesDb,
  aiRulesVectordb,
)

const workflowExecutionsDb = new MongoAiWorkflowExecutionRepository(driver)
export const workflowExecutionsBusinessLogic =
  new AiWorkflowExecutionBusinessLogic(workflowExecutionsDb)

const contactsDb = new MongoContactRepository(driver)
export const contactsBusinessLogic = new ContactBusinessLogic(contactsDb)

const customerProfilesDb = new MongoCustomerProfileRepository(driver)
export const customerProfilesBusinessLogic = new CustomerProfileBusinessLogic(
  customerProfilesDb,
)

const datasourcesDb = new MongoDatasourceRepository(driver)
const datasourcesVectordb = new PineconeDatasourceVectorDBRepository(
  process.env.PINECONE_API_KEY!,
)
export const datasourcesBusinessLogic = new DatasourceBusinessLogic(
  datasourcesDb,
  datasourcesVectordb,
)

const messageTemplatesDb = new MongoMessageTemplateRepository(driver)
const messageTemplatesVectordb = new PineconeMessageTemplateVectorDBRepository(
  process.env.PINECONE_API_KEY!,
)
export const messageTemplatesBusinessLogic = new MessageTemplateBusinessLogic(
  messageTemplatesDb,
  messageTemplatesVectordb,
)

const conversationMessagesDb = new MongoConversationMessageRepository(driver)
export const conversationMessagesBusinessLogic =
  new ConversationMessageBusinessLogic(conversationMessagesDb)

const conversationsDb = new MongoConversationRepository(driver)
export const conversationBusinessLogic = new ConversationBusinessLogic(
  conversationsDb,
)

const testConversationsDb = new MongoTestConversationRepository(driver)
export const testConversationBusinessLogic = new TestConversationBusinessLogic(
  testConversationsDb,
)

// Users
const usersDb = new MongoUserRepository(driver)
export const usersBusinessLogic = new UserBusinessLogic(usersDb)

// Account (uses Users business logic)
const accountDb = new AccountDBRepository(usersBusinessLogic)
export const accountBusinessLogic = new AccountBusinessLogicImpl(accountDb)

// Knowledge Base
const knowledgeBaseDb = new MongoKnowledgeBaseRepository(driver)
const knowledgeBaseParsedDb = new MongoKnowledgeBaseParsedRepository(driver)
const knowledgeBaseParsedVectordb = new PineconeKnowledgeBaseParsedVectorDBRepository(
  process.env.PINECONE_API_KEY!,
)
export const knowledgeBaseBusinessLogic = new KnowledgeBaseBusinessLogic(
  knowledgeBaseDb,
  knowledgeBaseParsedDb,
  knowledgeBaseParsedVectordb,
  {
    dataSource: datasourcesBusinessLogic,
    aiRule: aiRulesBusinessLogic,
    messageTemplate: messageTemplatesBusinessLogic,
  },
  new KnowledgebaseParserService(),
)

const broadcastDb = new MongoBroadcastRepository(driver)
export const broadcastBusinessLogic = new BroadcastBusinessLogic(broadcastDb)

const devicesDb = new MongoDeviceRepository(driver)
export const devicesBusinessLogic = new DeviceBusinessLogicImpl(devicesDb)
