import {
  MessageTemplate,
  MessageTemplateCreateInput,
  MessageTemplateUpdateInput,
  MessageTemplateQueryParams,
  MessageTemplateBusinessLogicInterface,
} from "./interface"
import { createError } from "@/lib/utils/common"
import { MessageTemplateDBRepository } from "./DBRepository"
import {
  MessageTemplateVectorDB,
  MessageTemplateVectorDBRepository,
} from "./VectorDBRepository"
import { SessionContext } from "../auth/types"

export class MessageTemplateBusinessLogic
  implements MessageTemplateBusinessLogicInterface {
  constructor(
    private readonly db: MessageTemplateDBRepository,
    private readonly vectordb: MessageTemplateVectorDBRepository,
  ) { }
  bulkCreate(data: MessageTemplateCreateInput[], context: SessionContext): Promise<MessageTemplate[]> {
    throw new Error("Method not implemented.")
  }
  bulkUpdate(updates: { id: string; data: MessageTemplateUpdateInput }[], context: SessionContext): Promise<number> {
    throw new Error("Method not implemented.")
  }
  bulkDelete(ids: string[], context: SessionContext, hardDelete?: boolean): Promise<number> {
    throw new Error("Method not implemented.")
  }

  private validateId(id: string) {
    if (!id || !id.trim())
      throw createError("MessageTemplate ID is required", "INVALID_ID")
  }

  private trimCreateInput(
    data: MessageTemplateCreateInput,
  ): MessageTemplateCreateInput {
    return {
      ...data,
      title: data.title.trim(),
      query: data.query?.trim() ?? "",
      template: data.template?.trim() ?? "",
      tags: data.tags?.map((t) => t.trim()) ?? [],
      variables: data.variables?.map((c) => c.trim()) ?? [],
      isActive: data.isActive ?? true,
    }
  }

  private trimUpdateInput(
    data: MessageTemplateUpdateInput,
  ): MessageTemplateUpdateInput {
    return {
      ...data,
      title: data.title?.trim(),
      query: data.query?.trim() ?? "",
      template: data.template?.trim(),
      tags: data.tags?.map((t) => t.trim()),
      variables: data.variables?.map((c) => c.trim()),
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<MessageTemplate | null> {
    this.validateId(id)
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted,
    }
    const result = await this.db.getAll(paramsWithContext)
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    params: MessageTemplateQueryParams,
    context: SessionContext,
  ): Promise<{ items: MessageTemplate[]; total: number }> {
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }
    return this.db.getAll(paramsWithContext)
  }

  async create(
    data: MessageTemplateCreateInput,
    context: SessionContext,
  ): Promise<MessageTemplate> {
    const trimmedData = this.trimCreateInput(data)

    // Add context information to the data
    const dataWithContext = {
      ...trimmedData,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    // Check for duplicate title within the same context
    // const contextFilters = this.buildContextFilters(context)
    // const existing = await this.db.getAll({
    //   filters: [
    //     { field: "title", value: trimmedData.title },
    //     ...contextFilters,
    //   ],
    // })
    // if (existing.items.length > 0) {
    //   throw createError(
    //     "Message Template with the same title already exists",
    //     "DUPLICATE_NAME",
    //   )
    // }

    const result = await this.db.create(dataWithContext)
    await this.vectordb.create({
      id: result.id,
      query: result.query,
      template: result.template,
    }, context)

    return result
  }

  async update(
    id: string,
    data: MessageTemplateUpdateInput,
    context: SessionContext,
  ): Promise<MessageTemplate | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    // Check if message template exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Message Template not found", "NOT_FOUND")
    }

    const existingRule = existingResult.items[0]

    if (data.title && data.title.trim() !== existingRule.title) {
      const duplicates = await this.db.getAll({
        filters: [
          { field: "title", value: data.title.trim() },
          ...contextFilters,
        ],
      })
      if (duplicates.items.some((r) => r.id !== id)) {
        throw createError(
          "Another Message Template with this title exists",
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = {
      ...this.trimUpdateInput(data),
      updatedBy: context.user.id,
    }

    await this.vectordb.update(id, {
      id,
      query: trimmedData.query || "",
      template: trimmedData.template || "",
    }, context)

    return this.db.update(id, trimmedData)
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if message template exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Message Template not found", "NOT_FOUND")
    }

    await this.vectordb.delete(id, context)

    return this.db.delete(id, hardDelete)
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)

    // Check if message template exists and belongs to the current context (including deleted)
    const contextFilters = this.buildContextFilters(context)
    const ruleResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted: true,
    })

    if (ruleResult.items.length === 0) return false

    const rule = ruleResult.items[0]
    if (!rule.deletedAt) return false

    // Check for conflicts by title within the same context
    const conflict = await this.db.getAll({
      filters: [{ field: "title", value: rule.title }, ...contextFilters],
    })
    if (conflict.items.length > 0) return false

    return this.db.restore(id)
  }


  async queryVectorDB(
    query: string,
    context: SessionContext,
    topK: number = 3,
  ): Promise<MessageTemplateVectorDB[]> {
    if (!query || !query.trim()) {
      return []
    }

    const searchTerm = query.trim()
    const vectorResults = await this.vectordb.search(searchTerm, topK, context)

    // Filter vector results to only include templates that belong to the current context
    const contextFilters = this.buildContextFilters(context)
    const filteredResults = []

    for (const vectorResult of vectorResults) {
      const templateResult = await this.db.getAll({
        filters: [{ field: "id", value: vectorResult.id }, ...contextFilters],
      })

      if (templateResult.items.length > 0) {
        filteredResults.push(vectorResult)
      }
    }

    return filteredResults
  }
}
