import {
  MessageTemplate,
  MessageTemplateCreateInput,
  MessageTemplateUpdateInput,
} from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type MessageTemplateDbQueryParams = BaseQueryParams<MessageTemplate>

export interface MessageTemplateDBRepository
  extends BaseDbRepository<
    MessageTemplate,
    MessageTemplateCreateInput & { createdBy: string; organizationId?: string },
    MessageTemplateUpdateInput,
    MessageTemplateDbQueryParams
  > { }
