import {
  MessageTemplate,
  MessageTemplateCreateInput,
  MessageTemplateUpdateInput,
  MessageTemplateQueryParams,
} from "./interface"
import { MongoDriver } from "../MongoDriver"
import {
  MessageTemplateDBRepository,
} from "./DBRepository"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { buildMongoQuery } from "../queryBuilder"
import { messageTemplatesSearchConfig } from "@/app/api/v1/search-configs/entities/messageTemplates"
import { generateId } from "@/lib/utils/common"

function mapMongoDocToMessageTemplate(doc: any): MessageTemplate | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return rest as MessageTemplate
}

export class MongoMessageTemplateRepository implements MessageTemplateDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.MESSAGE_TEMPLATES)
  }

  async getById(id: string, includeDeleted = false): Promise<MessageTemplate | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }
    const doc = await this.collection.findOne(query)
    return mapMongoDocToMessageTemplate(doc)
  }

  async getAll(params: MessageTemplateQueryParams): Promise<{ items: MessageTemplate[]; total: number }> {
    const initialQuery = messageTemplatesSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query, sort, limit, offset } = buildMongoQuery(
      initialQuery,
      {
        ...params,
        offset: params?.page,
      },
      messageTemplatesSearchConfig.searchableFields,
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)
    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToMessageTemplate)
      .filter((i): i is MessageTemplate => i !== null)

    const total = await this.collection.countDocuments(query)
    return { items, total }
  }

  async getCount(params: MessageTemplateQueryParams): Promise<{ total: number }> {
    const initialQuery = messageTemplatesSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query } = buildMongoQuery(
      initialQuery,
      params,
      messageTemplatesSearchConfig.searchableFields,
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: MessageTemplateCreateInput & { createdBy: string; organizationId?: string }): Promise<MessageTemplate> {
    const now = new Date()
    const doc = {
      ...data,
      id: generateId(),
      createdAt: now,
      updatedAt: now,
    }
    await this.collection.insertOne(doc)
    return mapMongoDocToMessageTemplate(doc)!
  }

  async update(id: string, data: MessageTemplateUpdateInput): Promise<MessageTemplate | null> {
    const existing = await this.collection.findOne({
      id,
      deletedAt: { $exists: false },
    })

    if (!existing) return null

    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() }).filter(
        ([_, value]) => value !== undefined,
      ),
    )

    const updateResult = await this.collection.updateOne(
      { id, deletedAt: { $exists: false } },
      { $set: updateData },
    )

    if (updateResult.modifiedCount === 0) return null

    const updated = await this.collection.findOne({ id })
    return mapMongoDocToMessageTemplate(updated)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount === 1
    } else {
      const result = await this.collection.updateOne(
        { id },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount === 1
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() },
      },
    )
    return result.modifiedCount === 1
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
