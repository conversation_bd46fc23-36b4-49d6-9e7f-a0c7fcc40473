import {
  AiWorkflowExecution,
  AiWorkflowExecutionCreateInput,
  AiWorkflowExecutionUpdateInput,
} from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type AiWorkflowExecutionDbQueryParams =
  BaseQueryParams<AiWorkflowExecution>

export interface AiWorkflowExecutionDBRepository
  extends BaseDbRepository<
    AiWorkflowExecution,
    AiWorkflowExecutionCreateInput & { createdBy: string; organizationId?: string },
    AiWorkflowExecutionUpdateInput,
    AiWorkflowExecutionDbQueryParams
  > { }
