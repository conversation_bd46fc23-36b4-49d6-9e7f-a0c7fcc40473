import { MONG<PERSON>_COLLECTIONS } from "@/lib/db/mongoCollections"
import { MongoDriver } from "../MongoDriver"
import { buildMongoQuery } from "../queryBuilder"
import { AiWorkflowExecutionDBRepository } from "./DBRepository"
import {
  AiWorkflowExecution,
  AiWorkflowExecutionCreateInput,
  AiWorkflowExecutionQueryParams,
  AiWorkflowExecutionUpdateInput,
} from "./interface"
import { workflowExecutionsSearchConfig } from "@/app/api/v1/search-configs/entities/workflowExecutions"
import { generateId } from "@/lib/utils/common"

function mapMongoDocToAiWorkflowExecution(doc: any): AiWorkflowExecution | null {
  if (!doc) return null
  return doc as AiWorkflowExecution
}

export class MongoAiWorkflowExecutionRepository
  implements AiWorkflowExecutionDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(
      MONGO_COLLECTIONS.AI_WORKFLOW_EXECUTIONS,
    )
  }

  async getById(id: string, includeDeleted = false): Promise<AiWorkflowExecution | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }
    const doc = await this.collection.findOne(query)
    return mapMongoDocToAiWorkflowExecution(doc)
  }

  async getAll(
    params: AiWorkflowExecutionQueryParams,
  ): Promise<{ items: AiWorkflowExecution[]; total: number }> {
    const initialQuery = workflowExecutionsSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query, sort, limit, offset } = buildMongoQuery(
      initialQuery,
      {
        ...params,
        offset: params?.page,
      },
      workflowExecutionsSearchConfig.searchableFields,
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)

    const docs = await cursor.toArray()

    const items = docs
      .map(mapMongoDocToAiWorkflowExecution)
      .filter((i): i is AiWorkflowExecution => i !== null)

    const total = await this.collection.countDocuments(query)

    return { items, total }
  }

  async getCount(
    params: AiWorkflowExecutionQueryParams,
  ): Promise<{ total: number }> {
    const initialQuery = workflowExecutionsSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query } = buildMongoQuery(
      initialQuery,
      params,
      workflowExecutionsSearchConfig.searchableFields,
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(
    data: AiWorkflowExecutionCreateInput & { createdBy: string; organizationId?: string },
  ): Promise<AiWorkflowExecution> {
    const now = new Date()
    const doc = {
      ...data,
      id: generateId(),
      createdAt: now,
      updatedAt: now,
    }
    await this.collection.insertOne(doc)
    return mapMongoDocToAiWorkflowExecution(doc)!
  }

  async update(
    id: string,
    data: AiWorkflowExecutionUpdateInput,
  ): Promise<AiWorkflowExecution | null> {
    const existing = await this.collection.findOne({
      id,
      deletedAt: { $exists: false },
    })
    if (!existing) return null

    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() }).filter(
        ([_, value]) => value !== undefined,
      ),
    )

    const updateResult = await this.collection.updateOne(
      { id, deletedAt: { $exists: false } },
      { $set: updateData },
    )

    if (updateResult.modifiedCount === 0) return null

    const updated = await this.collection.findOne({ id })
    return mapMongoDocToAiWorkflowExecution(updated)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount === 1
    } else {
      const result = await this.collection.updateOne(
        { id },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount === 1
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() },
      },
    )
    return result.modifiedCount === 1
  }

  async bulkCreate(data: AiWorkflowExecutionCreateInput[]): Promise<AiWorkflowExecution[]> {
    const now = new Date()
    const docs = data.map((d) => ({
      ...d,
      id: generateId(),
      createdAt: now,
      updatedAt: now,
    }))
    await this.collection.insertMany(docs)
    return docs.map(mapMongoDocToAiWorkflowExecution).filter(Boolean) as AiWorkflowExecution[]
  }

  async bulkUpdate(
    updates: { id: string; data: AiWorkflowExecutionUpdateInput }[],
  ): Promise<number> {
    let count = 0
    for (const { id, data } of updates) {
      const result = await this.collection.updateOne(
        { id },
        { $set: { ...data, updatedAt: new Date() } },
      )
      if (result.modifiedCount) count++
    }
    return count
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (hardDelete) {
      const result = await this.collection.deleteMany({
        id: { $in: ids },
      })
      return result.deletedCount ?? 0
    } else {
      const result = await this.collection.updateMany(
        { id: { $in: ids } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount ?? 0
    }
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
