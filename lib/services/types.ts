export interface PagingAndSearch<
  T extends Record<string, string | boolean | string[] | undefined>,
> {
  page: number
  per_page?: number
  search?: string
  sort?: { field: string; direction: "ASC" | "DESC" }[]
  filters?: {
    field: string
    value: string | string[] | boolean
    operator?: string
  }[]
}

export namespace PagingAndSearch {
  export interface PagingAndSearch<T> {
    page: number
    per_page?: number
    search?: string
    sort?: Array<{ field: string; direction: "ASC" | "DESC" }>
    filters?: Array<{
      field: keyof T | string
      value: string | string[] | boolean | undefined
      operator?: string
    }>
  }

  export function toRecordString<
    T extends Record<string, string | boolean | string[] | undefined>,
  >(p: PagingAndSearch<T>): Record<string, string> {
    const query: Record<string, string> = {
      page: p.page.toString(),
      limit: (p.per_page ?? 20).toString(),
    }

    if (p.search) {
      query.search = p.search
    }

    if (p.sort?.length) {
      for (const s of p.sort) {
        query[`sort.${s.field}`] = s.direction
      }
    }

    if (p.filters?.length) {
      for (const filter of p.filters) {
        if (filter.value !== undefined) {
          const value = Array.isArray(filter.value)
            ? filter.value.join(",")
            : filter.value.toString()
          query[`filter.${filter.field.toString()}`] =
            `${filter.operator ? filter.operator + ":" : ""}${value}`
        }
      }
    }

    return query
  }
}

export interface PaginatedResponse<T> {
  items: T[]
  page: number
  total: number
}
