import { Conversation, ConversationCloseMetadata } from "@/lib/repositories/conversations/interface"
import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"

type ConversationPayload = Partial<Conversation>

interface CloseConversationPayload {
  reason?: string
  notes?: string
}

export class ConversationsAPI extends BaseAPI {
  static All(params: PagingAndSearch<{}>) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/conversations${queryString}`).build<
      PaginatedResponse<Conversation>
    >()
  }

  static Detail = (conversationId: string) =>
    new BaseAPI(`/conversations/${conversationId}`).build<Conversation>()

  static Create = (body: ConversationPayload) =>
    new BaseAPI(`/conversations`, body, "POST").build<Conversation>()

  static Update = (conversationId: string, body: ConversationPayload) =>
    new BaseAPI(
      `/conversations/${conversationId}`,
      body,
      "PUT",
    ).build<Conversation>()

  static Close = (conversationId: string, body?: CloseConversationPayload) =>
    new BaseAPI(
      `/conversations/${conversationId}/close`,
      body,
      "POST",
    ).build<Conversation>()

  static Delete = (conversationId: string) =>
    new BaseAPI(`/conversations/${conversationId}`, undefined, "DELETE").build<{
      success: boolean
    }>()

  static BulkCreate = (data: ConversationPayload[]) =>
    new BaseAPI(
      `/conversations/bulk`,
      { operation: "import", data },
      "POST",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkUpdate = (data: Array<{ id: string;[key: string]: any }>) =>
    new BaseAPI(
      `/conversations/bulk`,
      { operation: "update", data },
      "PUT",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkDelete = (ids: string[]) =>
    new BaseAPI(
      `/conversations/bulk`,
      { operation: "delete", ids },
      "DELETE",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ id?: string; message: string }>
    }>()

  static Stats = (params: {
    search?: string
    includeDeleted?: boolean
    filters?: Array<{ field: string; value: any }>
    dateFrom?: string
    dateTo?: string
  }) => {
    const queryParams = new URLSearchParams()
    if (params?.search) queryParams.append("search", params.search)
    if (params?.includeDeleted) queryParams.append("includeDeleted", "true")
    if (params?.filters)
      queryParams.append("filters", JSON.stringify(params.filters))
    if (params?.dateFrom) queryParams.append("dateFrom", params.dateFrom)
    if (params?.dateTo) queryParams.append("dateTo", params.dateTo)

    const queryString = queryParams.toString()
      ? `?${queryParams.toString()}`
      : ""
    return new BaseAPI(`/conversations/stats${queryString}`).build<{
      totalConversations: number
      activeConversations: number
      deletedConversations: number
      conversationsWithTags: number
      recentConversations: number
      statusBreakdown: Array<{
        status: string
        count: number
        percentage: number
      }>
      tagBreakdown: Array<{ tag: string; count: number; percentage: number }>
      createdByBreakdown: Array<{
        createdBy: string
        count: number
        percentage: number
      }>
      dailyStats: Array<{ date: string; created: number; deleted: number }>
    }>()
  }

  static AIAnswer = (conversationId: string, messageId: string) =>
    new BaseAPI(
      `/conversations/${conversationId}/ai-answer?messageId=${messageId}`,
      undefined,
      "POST",
    ).build<{ executionId: string }>()
}
