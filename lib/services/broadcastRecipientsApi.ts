import { Base<PERSON><PERSON> } from "./baseApi"
import { PagingAndSearch } from "./types"

export interface BroadcastRecipientContact {
  id: string
  name: string
  phone: string
  email?: string
  tags: string[] // Always array, never undefined
}

export interface TagWithCount {
  tag: string
  count: number
}

export interface BroadcastRecipientsResponse {
  contacts: BroadcastRecipientContact[]
  total: number
  page: number
  limit: number
  totalPages: number
  availableTags: TagWithCount[]
  selectedCount: number
}

export class BroadcastRecipientsAPI extends BaseAPI {
  // Get contacts for broadcast with filtering and tag selection
  static All(params?: PagingAndSearch<{
    tags?: string[]
    excludedIds?: string[]
    includeDeleted?: boolean
  }>) {
    const queryString = params
      ? `?${new URLSearchParams(PagingAndSearch.toRecordString(params)).toString()}`
      : ""

    return new BaseAPI(`/broadcast-recipients${queryString}`).build<BroadcastRecipientsResponse>()
  }

  // Get all available tags from contacts
  static GetAvailableTags() {
    return new BaseAPI("/broadcast-recipients?limit=1")
      .build<BroadcastRecipientsResponse>()
  }

  // Get recipient count for specific tags and exclusions
  static GetRecipientCount(tags?: string[], excludedIds?: string[]) {
    const searchParams = new URLSearchParams()
    searchParams.set("limit", "1") // We only need the count

    if (tags && tags.length > 0) {
      searchParams.set("tags", tags.join(","))
    }
    if (excludedIds && excludedIds.length > 0) {
      searchParams.set("excludedIds", excludedIds.join(","))
    }

    const url = `/broadcast-recipients?${searchParams.toString()}`

    return new BaseAPI(url).build<BroadcastRecipientsResponse>()
  }
}
