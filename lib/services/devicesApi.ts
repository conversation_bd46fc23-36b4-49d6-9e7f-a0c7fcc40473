import { BaseAPI } from "./baseApi"
import { PagingAndSearch, PaginatedResponse } from "./types"
import { Device, DeviceStatus } from "@/lib/repositories/devices/interface"

export interface DevicePayload {
  sessionId: string
  name: string
  platform: string
  status: DeviceStatus
  providerName: string
  providerData?: any
  me?: {
    pushName?: string
    id?: string
    [key: string]: any
  }
  isActive?: boolean
  lastSeenAt?: Date
  linkedAt?: Date
}

export interface DeviceUpdatePayload {
  name?: string
  platform?: string
  status?: DeviceStatus
  providerData?: any
  me?: {
    pushName?: string
    id?: string
    [key: string]: any
  }
  isActive?: boolean
  lastSeenAt?: Date
}

export interface DeviceQueryParams extends PagingAndSearch<{}> {
  status?: DeviceStatus
  providerName?: string
  isActive?: boolean
}

export interface SyncResult {
  synced: number
  created: number
  updated: number
  errors: string[]
}

export interface DeviceRemoveResponse {
  success: boolean
  message?: string
}

export class DevicesAPI extends BaseAPI {
  static All(params: DeviceQueryParams) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/devices${queryString}`).build<
      PaginatedResponse<Device> & { syncResult: SyncResult }
    >()
  }

  static Detail = (deviceId: string) =>
    new BaseAPI(`/devices/${deviceId}`).build<Device>()

  static Create = (body: DevicePayload) =>
    new BaseAPI(`/devices`, body, "POST").build<Device>()

  static Update = (deviceId: string, body: DeviceUpdatePayload) =>
    new BaseAPI(`/devices/${deviceId}`, body, "PUT").build<Device>()

  static Delete = (deviceId: string) =>
    new BaseAPI(`/devices/${deviceId}`, undefined, "DELETE").build<{
      deleted: boolean
    }>()

  static Sync = () =>
    new BaseAPI(`/devices`, { action: "sync" }, "POST").build<SyncResult>()

  static SyncAll = () =>
    new BaseAPI(`/devices/sync-all`, {}, "POST").build<SyncResult>()

  static Link = (body: { name: string }) =>
    new BaseAPI("/devices/link", body, "POST").build<{
      sessionId: string
      device: Device
    }>()

  static GetQR = (deviceId: string) =>
    new BaseAPI(`/devices/${deviceId}/qr/`, undefined, "GET").build<{
      qr: string
      session: string
    }>()

  static ToggleState = (deviceId: string, isActive: boolean) =>
    new BaseAPI(`/devices/${deviceId}/toggle-state`, { isActive }, "POST").build<Device>()
}
