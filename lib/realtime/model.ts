import { realtime, RealtimeChannel } from "."
import { AiWorkflowExecution, AiWorkflowStep } from "@/lib/repositories/aiWorkflowExecutions"
import { Conversation } from "@/lib/repositories/conversations"
import { ConversationMessage } from "@/lib/repositories/conversationMessages"
import { TestMessage } from "../repositories/testConversations"

class RealtimeMessage<T> {
    constructor(readonly CHANNEL: string, readonly EVENT: string, private data: T) { }
    async send() {
        await realtime.server.trigger(this.CHANNEL, this.EVENT, this.data)
    }
}


export class RealtimeWorkflow<T> extends RealtimeMessage<T> {
    static readonly CHANNEL_NAME = "workflow-channel"

    constructor(readonly event: string, userId: string, data: T) {
        super(RealtimeWorkflow.CHANNEL_NAME + "-" + userId, event, data)
    }

    // Server-side methods
    static NEW_WORKFLOW(workflow: AiWorkflowExecution) {
        return new RealtimeWorkflow("new-workflow", workflow.createdBy, { workflow })
    }
    static NEW_WORKFLOW_STEP(workflowId: string, newStep: AiWorkflowStep) {
        return new RealtimeWorkflow("new-workflow-step", newStep.createdBy, { workflowId, newStep })
    }

    // Client-side subscription method
    static subscribe(userId: string) {
        return realtime.client.subscribe(RealtimeWorkflow.CHANNEL_NAME + "-" + userId)
    }

    static LISTEN_NEW_WORKFLOW(channel: RealtimeChannel, callback: (workflow: AiWorkflowExecution) => void) {
        channel.bind("new-workflow", (data: any) => {
            callback(data.workflow)
        })
    }

    static LISTEN_NEW_WORKFLOW_STEP(channel: RealtimeChannel, callback: (workflowId: string, newStep: AiWorkflowStep) => void) {
        channel.bind("new-workflow-step", (data: any) => {
            callback(data.workflowId, data.newStep)
        })
    }
}

export class RealtimeConversationRoom<T> extends RealtimeMessage<T> {
    static readonly CHANNEL_NAME = "conversation-room-channel"

    constructor(readonly event: string, conversationId: string, data: any) {
        super(RealtimeConversationRoom.CHANNEL_NAME + "-" + conversationId, event, data)
    }

    // Server-side methods
    static NEW_MESSAGE(conversationId: string, message: ConversationMessage) {
        return new RealtimeConversationRoom("new-message", conversationId, {
            conversationId,
            message
        })
    }
    static UPDATE_MESSAGE(conversationId: string) {
        return new RealtimeConversationRoom("message-update", conversationId, conversationId)
    }

    static UPDATE_CONVERSATION(conversation: Conversation) {
        return new RealtimeConversationRoom("conversation-updated", conversation.id, {
            conversation
        })
    }

    // Client-side subscription method
    static subscribe(conversationId: string) {
        return realtime.client.subscribe(RealtimeConversationRoom.CHANNEL_NAME + "-" + conversationId)
    }

    static LISTEN_NEW_MESSAGE(channel: RealtimeChannel, callback: (conversationId: string, message: ConversationMessage) => void) {
        channel.bind("new-message", (data: any) => {
            callback(data.conversationId, data.message)
        })
    }

    static LISTEN_UPDATE_MESSAGE(channel: RealtimeChannel, callback: (conversationId: string) => void) {
        channel.bind("message-update", (data: any) => {
            callback(data.conversationId)
        })
    }

    static LISTEN_UPDATE_CONVERSATION(channel: RealtimeChannel, callback: (conversation: Conversation) => void) {
        channel.bind("conversation-updated", (data: any) => {
            callback(data.conversation)
        })
    }
}

export class RealtimeTestConversationRoom<T> extends RealtimeMessage<T> {
    static readonly CHANNEL_NAME = "test-conversation-room-channel"

    constructor(readonly event: string, conversationId: string, data: any) {
        super(RealtimeConversationRoom.CHANNEL_NAME + "-" + conversationId, event, data)
    }

    // Server-side methods
    static NEW_MESSAGE(conversationId: string, message: TestMessage) {
        return new RealtimeConversationRoom("new-message", conversationId, {
            conversationId,
            message
        })
    }

    // Client-side subscription method
    static subscribe(conversationId: string) {
        return realtime.client.subscribe(RealtimeConversationRoom.CHANNEL_NAME + "-" + conversationId)
    }

    static LISTEN_NEW_MESSAGE(channel: RealtimeChannel, callback: (conversationId: string, message: TestMessage) => void) {
        channel.bind("new-message", (data: any) => {
            callback(data.conversationId, data.message)
        })
    }
}

export class RealtimeConversationList<T> extends RealtimeMessage<T> {
    static readonly CHANNEL_NAME = "conversation-list"

    constructor(readonly event: string, userId: string, data: T) {
        super(RealtimeConversationList.CHANNEL_NAME + "-" + userId, event, data)
    }

    static UPDATE_CONVERSATION(conversation: Conversation) {
        return new RealtimeConversationList("conversation-updated", conversation.createdBy, {
            conversation
        })
    }

    static NEW_CONVERSATION(conversation: Conversation) {
        return new RealtimeConversationList("new-conversation", conversation.createdBy, {
            conversation
        })
    }

    // Client-side subscription method
    static subscribe(userId: string) {
        return realtime.client.subscribe(RealtimeConversationList.CHANNEL_NAME + "-" + userId)
    }

    static LISTEN_UPDATE_COVERSATION(channel: RealtimeChannel, callback: (conversation: Conversation) => void) {
        channel.bind("conversation-updated", (data: any) => {
            callback(data.conversation)
        })
    }

    static LISTEN_NEW_CONVERSATION(channel: RealtimeChannel, callback: (conversation: Conversation) => void) {
        channel.bind("new-conversation", (data: any) => {
            callback(data.conversation)
        })
    }
}