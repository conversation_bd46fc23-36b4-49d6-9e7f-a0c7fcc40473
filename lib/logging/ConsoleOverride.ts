import { Logger } from './Logger'
import { LogMetadata } from './types'

// Store original console methods
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug
}

let isOverridden = false
let globalLogger: Logger | null = null

export class ConsoleOverride {
  static setup(logger: Logger): void {
    if (isOverridden) {
      return // Already overridden
    }

    globalLogger = logger
    isOverridden = true

    // Override console methods
    console.log = (...args: any[]) => {
      const { message, metadata } = parseConsoleArgs(args)
      logger.info(message, metadata)
      
      // Also call original for development
      if (process.env.NODE_ENV === 'development') {
        originalConsole.log(...args)
      }
    }

    console.info = (...args: any[]) => {
      const { message, metadata } = parseConsoleArgs(args)
      logger.info(message, metadata)
      
      if (process.env.NODE_ENV === 'development') {
        originalConsole.info(...args)
      }
    }

    console.warn = (...args: any[]) => {
      const { message, metadata } = parseConsoleArgs(args)
      logger.warn(message, metadata)
      
      if (process.env.NODE_ENV === 'development') {
        originalConsole.warn(...args)
      }
    }

    console.error = (...args: any[]) => {
      const { message, metadata, error } = parseConsoleArgs(args, true)
      logger.error(message, error, metadata)
      
      if (process.env.NODE_ENV === 'development') {
        originalConsole.error(...args)
      }
    }

    console.debug = (...args: any[]) => {
      const { message, metadata } = parseConsoleArgs(args)
      logger.debug(message, metadata)
      
      if (process.env.NODE_ENV === 'development') {
        originalConsole.debug(...args)
      }
    }
  }

  static restore(): void {
    if (!isOverridden) {
      return
    }

    // Restore original console methods
    console.log = originalConsole.log
    console.info = originalConsole.info
    console.warn = originalConsole.warn
    console.error = originalConsole.error
    console.debug = originalConsole.debug

    isOverridden = false
    globalLogger = null
  }

  static getOriginalConsole() {
    return originalConsole
  }

  static isActive(): boolean {
    return isOverridden
  }
}

function parseConsoleArgs(args: any[], includeError = false): { 
  message: string
  metadata?: LogMetadata
  error?: Error 
} {
  if (args.length === 0) {
    return { message: '' }
  }

  let message = ''
  let metadata: LogMetadata = {}
  let error: Error | undefined

  // Handle different argument patterns
  if (args.length === 1) {
    const arg = args[0]
    
    if (typeof arg === 'string') {
      message = arg
    } else if (arg instanceof Error && includeError) {
      message = arg.message
      error = arg
    } else if (typeof arg === 'object' && arg !== null) {
      message = JSON.stringify(arg)
      metadata = { data: arg }
    } else {
      message = String(arg)
    }
  } else {
    // Multiple arguments
    const firstArg = args[0]
    
    if (typeof firstArg === 'string') {
      // String message with additional data
      message = firstArg
      
      // Process remaining arguments
      for (let i = 1; i < args.length; i++) {
        const arg = args[i]
        
        if (arg instanceof Error && includeError && !error) {
          error = arg
        } else if (typeof arg === 'object' && arg !== null) {
          // Merge object properties into metadata
          Object.assign(metadata, arg)
        } else {
          // Add as additional data
          metadata[`arg${i}`] = arg
        }
      }
    } else {
      // No string message, combine all arguments
      message = args.map(arg => {
        if (typeof arg === 'string') return arg
        if (typeof arg === 'object') return JSON.stringify(arg)
        return String(arg)
      }).join(' ')
    }
  }

  return { message, metadata: Object.keys(metadata).length > 0 ? metadata : undefined, error }
}

// Enhanced console methods with explicit metadata support
export const enhancedConsole = {
  log: (message: string, metadata?: LogMetadata) => {
    if (globalLogger) {
      globalLogger.info(message, metadata)
    } else {
      originalConsole.log(message, metadata)
    }
  },

  info: (message: string, metadata?: LogMetadata) => {
    if (globalLogger) {
      globalLogger.info(message, metadata)
    } else {
      originalConsole.info(message, metadata)
    }
  },

  warn: (message: string, metadata?: LogMetadata) => {
    if (globalLogger) {
      globalLogger.warn(message, metadata)
    } else {
      originalConsole.warn(message, metadata)
    }
  },

  error: (message: string, error?: Error, metadata?: LogMetadata) => {
    if (globalLogger) {
      globalLogger.error(message, error, metadata)
    } else {
      originalConsole.error(message, error, metadata)
    }
  },

  debug: (message: string, metadata?: LogMetadata) => {
    if (globalLogger) {
      globalLogger.debug(message, metadata)
    } else {
      originalConsole.debug(message, metadata)
    }
  }
}
