// Logging types and interfaces

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal'
}

export interface LogMetadata {
  [key: string]: any
  userId?: string
  organizationId?: string
  sessionId?: string
  requestId?: string
  userAgent?: string
  ip?: string
  endpoint?: string
  method?: string
  statusCode?: number
  duration?: number
  component?: string
  feature?: string
  action?: string
  entityId?: string
  entityType?: string
}

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  metadata?: LogMetadata
  error?: {
    name: string
    message: string
    stack?: string
    code?: string | number
  }
  source: {
    file?: string
    function?: string
    line?: number
    column?: number
  }
}

export interface LogTransport {
  name: string
  send(entry: LogEntry): Promise<void>
}

export interface LoggerConfig {
  level: LogLevel
  transports: LogTransport[]
  defaultMetadata?: LogMetadata
  enableConsole?: boolean
  enableStackTrace?: boolean
}

export interface GrafanaLogConfig {
  endpoint: string
  apiKey?: string
  username?: string
  password?: string
  datasource?: string
  labels?: Record<string, string>
  batchSize?: number
  flushInterval?: number
  retryAttempts?: number
  retryDelay?: number
}
