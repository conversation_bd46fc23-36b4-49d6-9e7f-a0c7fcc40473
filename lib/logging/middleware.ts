import { NextRequest, NextResponse } from 'next/server'
import { createContextLogger } from './index'
import { v4 as uuidv4 } from 'uuid'

export interface RequestLogContext {
  requestId: string
  method: string
  url: string
  userAgent?: string
  ip?: string
  userId?: string
  organizationId?: string
  startTime: number
}

// Middleware function for automatic request logging
export function withRequestLogging<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>
) {
  return async (...args: T): Promise<NextResponse> => {
    const req = args[0] as NextRequest
    const startTime = Date.now()
    const requestId = uuidv4()
    
    // Extract request information
    const context: RequestLogContext = {
      requestId,
      method: req.method,
      url: req.url,
      userAgent: req.headers.get('user-agent') || undefined,
      ip: getClientIP(req),
      startTime
    }

    // Create context logger
    const logger = createContextLogger({
      requestId: context.requestId,
      component: 'api'
    })

    // Log request start
    logger.info(`${context.method} ${context.url}`, {
      method: context.method,
      url: context.url,
      userAgent: context.userAgent,
      ip: context.ip,
      event: 'request_start'
    })

    let response: NextResponse
    let error: Error | null = null

    try {
      // Execute the handler
      response = await handler(...args)
    } catch (err) {
      error = err instanceof Error ? err : new Error(String(err))
      
      // Log error
      logger.error(`${context.method} ${context.url} - Error`, error, {
        method: context.method,
        url: context.url,
        duration: Date.now() - startTime,
        event: 'request_error'
      })

      // Create error response
      response = NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      )
    }

    const duration = Date.now() - startTime
    const statusCode = response.status

    // Log request completion
    if (error) {
      logger.error(`${context.method} ${context.url} - ${statusCode}`, error, {
        method: context.method,
        url: context.url,
        statusCode,
        duration,
        event: 'request_complete'
      })
    } else {
      logger.info(`${context.method} ${context.url} - ${statusCode}`, {
        method: context.method,
        url: context.url,
        statusCode,
        duration,
        event: 'request_complete'
      })
    }

    return response
  }
}

// Helper function to extract client IP
function getClientIP(req: NextRequest): string | undefined {
  const forwarded = req.headers.get('x-forwarded-for')
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  const realIP = req.headers.get('x-real-ip')
  if (realIP) {
    return realIP
  }
  
  const cfConnectingIP = req.headers.get('cf-connecting-ip')
  if (cfConnectingIP) {
    return cfConnectingIP
  }
  
  return undefined
}

// Decorator for API route handlers
export function loggedApiRoute(
  handler: (req: NextRequest, context?: any) => Promise<NextResponse>
) {
  return withRequestLogging(handler)
}

// Helper to add user context to existing logger
export function addUserContext(
  logger: ReturnType<typeof createContextLogger>,
  userId: string,
  organizationId?: string
) {
  return logger.child({
    userId,
    organizationId
  })
}

// Performance logging helper
export function logPerformance(
  operation: string,
  startTime: number,
  metadata?: any
) {
  const duration = Date.now() - startTime
  const logger = createContextLogger({ component: 'performance' })
  
  if (duration > 1000) {
    logger.warn(`Slow operation: ${operation}`, {
      operation,
      duration,
      ...metadata
    })
  } else {
    logger.debug(`Operation completed: ${operation}`, {
      operation,
      duration,
      ...metadata
    })
  }
}

// Database operation logging helper
export function logDatabaseOperation(
  operation: string,
  table: string,
  startTime: number,
  metadata?: any
) {
  const duration = Date.now() - startTime
  const logger = createContextLogger({ component: 'database' })
  
  logger.debug(`DB ${operation}: ${table}`, {
    operation,
    table,
    duration,
    ...metadata
  })
}

// External service logging helper
export function logExternalService(
  service: string,
  operation: string,
  startTime: number,
  success: boolean,
  metadata?: any
) {
  const duration = Date.now() - startTime
  const logger = createContextLogger({ component: 'external' })
  
  if (success) {
    logger.info(`External ${service}: ${operation}`, {
      service,
      operation,
      duration,
      success,
      ...metadata
    })
  } else {
    logger.error(`External ${service}: ${operation} failed`, undefined, {
      service,
      operation,
      duration,
      success,
      ...metadata
    })
  }
}
