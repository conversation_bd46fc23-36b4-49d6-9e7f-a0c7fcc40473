import { ConsoleOverride } from '../ConsoleOverride'
import { LogTransport, LogEntry, LogLevel } from '../types'

const originalConsole = ConsoleOverride.getOriginalConsole()

export class ConsoleTransport implements LogTransport {
  public readonly name = 'console'

  async send(entry: LogEntry): Promise<void> {
    const formattedMessage = this.formatMessage(entry)
    switch (entry.level) {
      case LogLevel.DEBUG:
        originalConsole.debug(formattedMessage)
        break
      case LogLevel.INFO:
        originalConsole.info(formattedMessage)
        break
      case LogLevel.WARN:
        originalConsole.warn(formattedMessage)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        originalConsole.error(formattedMessage)
        break
      default:
        originalConsole.log(formattedMessage)
    }
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toISOString()
    const level = entry.level.toUpperCase().padEnd(5)

    let message = `[${timestamp}]${level} ${entry.message}`

    if (entry.metadata && Object.keys(entry.metadata).length > 0) {
      message += ` | Metadata: ${JSON.stringify(entry.metadata)}`
    }

    if (entry.error) {
      message += ` | Error: ${entry.error.name}: ${entry.error.message}`
      if (entry.error.stack) {
        message += `\n${entry.error.stack}`
      }
    }

    return message
  }
}
