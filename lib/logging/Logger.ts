import { LogLevel, LogEntry, LogMetadata, LogTransport, LoggerConfig } from './types'

export class Logger {
  private config: LoggerConfig
  private defaultMetadata: LogMetadata

  constructor(config: LoggerConfig) {
    this.config = config
    this.defaultMetadata = config.defaultMetadata || {}
  }

  // Main logging methods
  debug(message: string, metadata?: LogMetadata): void {
    this.log(LogLevel.DEBUG, message, metadata)
  }

  info(message: string, metadata?: LogMetadata): void {
    this.log(LogLevel.INFO, message, metadata)
  }

  warn(message: string, metadata?: LogMetadata): void {
    this.log(LogLevel.WARN, message, metadata)
  }

  error(message: string, error?: Error | any, metadata?: LogMetadata): void {
    const errorData = error ? this.serializeError(error) : undefined
    this.log(LogLevel.ERROR, message, metadata, errorData)
  }

  fatal(message: string, error?: Error | any, metadata?: LogMetadata): void {
    const errorData = error ? this.serializeError(error) : undefined
    this.log(LogLevel.FATAL, message, metadata, errorData)
  }

  // Convenience methods for common use cases
  apiRequest(method: string, endpoint: string, statusCode: number, duration: number, metadata?: LogMetadata): void {
    this.info(`${method} ${endpoint}`, {
      ...metadata,
      method,
      endpoint,
      statusCode,
      duration,
      component: 'api'
    })
  }

  apiError(method: string, endpoint: string, error: Error, metadata?: LogMetadata): void {
    this.error(`API Error: ${method} ${endpoint}`, error, {
      ...metadata,
      method,
      endpoint,
      component: 'api'
    })
  }

  userAction(action: string, userId: string, metadata?: LogMetadata): void {
    this.info(`User action: ${action}`, {
      ...metadata,
      action,
      userId,
      component: 'user'
    })
  }

  businessLogic(feature: string, action: string, entityType?: string, entityId?: string, metadata?: LogMetadata): void {
    this.info(`${feature}: ${action}`, {
      ...metadata,
      feature,
      action,
      entityType,
      entityId,
      component: 'business'
    })
  }

  database(operation: string, table: string, duration?: number, metadata?: LogMetadata): void {
    this.debug(`DB ${operation}: ${table}`, {
      ...metadata,
      operation,
      table,
      duration,
      component: 'database'
    })
  }

  external(service: string, operation: string, duration?: number, metadata?: LogMetadata): void {
    this.info(`External ${service}: ${operation}`, {
      ...metadata,
      service,
      operation,
      duration,
      component: 'external'
    })
  }

  // Core logging method
  private log(level: LogLevel, message: string, metadata?: LogMetadata, error?: any): void {
    // Check if log level is enabled
    if (!this.shouldLog(level)) {
      return
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      metadata: { ...this.defaultMetadata, ...metadata },
      error,
      source: this.getSourceInfo()
    }

    // Send to all transports
    this.config.transports.forEach(transport => {
      transport.send(entry).catch(err => {
        console.error(`Failed to send log to ${transport.name}:`, err)
      })
    })
  }

  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.FATAL]
    const configLevelIndex = levels.indexOf(this.config.level)
    const logLevelIndex = levels.indexOf(level)
    return logLevelIndex >= configLevelIndex
  }

  private serializeError(error: any): any {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: this.config.enableStackTrace ? error.stack : undefined,
        code: (error as any).code
      }
    }
    
    if (typeof error === 'object' && error !== null) {
      return {
        name: error.name || 'Unknown',
        message: error.message || String(error),
        code: error.code
      }
    }
    
    return {
      name: 'Unknown',
      message: String(error)
    }
  }

  private getSourceInfo(): any {
    if (!this.config.enableStackTrace) {
      return {}
    }

    const stack = new Error().stack
    if (!stack) return {}

    const lines = stack.split('\n')
    // Skip the first few lines (Error, getSourceInfo, log method)
    const callerLine = lines[4] || ''
    
    const match = callerLine.match(/at\s+(.+?)\s+\((.+):(\d+):(\d+)\)/) || 
                  callerLine.match(/at\s+(.+):(\d+):(\d+)/)
    
    if (match) {
      return {
        function: match[1],
        file: match[2],
        line: parseInt(match[3]),
        column: parseInt(match[4])
      }
    }
    
    return {}
  }

  // Child logger with additional default metadata
  child(metadata: LogMetadata): Logger {
    return new Logger({
      ...this.config,
      defaultMetadata: { ...this.defaultMetadata, ...metadata }
    })
  }

  // Cleanup method
  async destroy(): Promise<void> {
    await Promise.all(
      this.config.transports.map(transport => {
        if ('destroy' in transport && typeof transport.destroy === 'function') {
          return transport.destroy()
        }
        return Promise.resolve()
      })
    )
  }
}
