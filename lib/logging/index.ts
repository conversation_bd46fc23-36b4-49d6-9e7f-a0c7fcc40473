// Main logging exports and setup
export * from './types'
export * from './Logger'
export * from './ConsoleOverride'
export * from './transports/GrafanaTransport'
export * from './transports/ConsoleTransport'

import { Logger } from './Logger'
import { LogLevel, LoggerConfig } from './types'
import { GrafanaTransport } from './transports/GrafanaTransport'
import { ConsoleTransport } from './transports/ConsoleTransport'
import { ConsoleOverride } from './ConsoleOverride'

// Global logger instance
let globalLogger: Logger | null = null

// Create and configure the global logger
export function setupLogging(config?: Partial<LoggerConfig>): Logger {
  const defaultConfig: LoggerConfig = {
    level: (process.env.LOG_LEVEL as LogLevel) || LogLevel.INFO,
    transports: [],
    enableConsole: process.env.NODE_ENV === 'development',
    enableStackTrace: process.env.NODE_ENV === 'development',
    defaultMetadata: {
      service: 'cs-ai-app',
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0'
    }
  }

  const finalConfig = { ...defaultConfig, ...config }

  // Add console transport if enabled
  if (finalConfig.enableConsole) {
    finalConfig.transports.push(new ConsoleTransport())
  }

  // Add Grafana transport if configured
  if (process.env.GRAFANA_LOKI_ENDPOINT) {
    const grafanaConfig = {
      endpoint: process.env.GRAFANA_LOKI_ENDPOINT,
      apiKey: process.env.GRAFANA_API_KEY,
      username: process.env.GRAFANA_USERNAME,
      password: process.env.GRAFANA_PASSWORD,
      labels: {
        service: 'cs-ai-app',
        environment: process.env.NODE_ENV || 'development'
      }
    }

    finalConfig.transports.push(new GrafanaTransport(grafanaConfig))
  }

  globalLogger = new Logger(finalConfig)

  // Override console methods if enabled
  if (process.env.OVERRIDE_CONSOLE !== 'false') {
    ConsoleOverride.setup(globalLogger)
  }

  return globalLogger
}

// Get the global logger instance
export function getLogger(): Logger {
  if (!globalLogger) {
    return setupLogging()
  }
  return globalLogger
}

// Convenience function to create a child logger with context
export function createContextLogger(context: {
  userId?: string
  organizationId?: string
  sessionId?: string
  requestId?: string
  component?: string
  feature?: string
}): Logger {
  return getLogger().child(context)
}

// Cleanup function
export async function shutdownLogging(): Promise<void> {
  if (globalLogger) {
    await globalLogger.destroy()
    globalLogger = null
  }

  ConsoleOverride.restore()
}

// Export a default logger instance for immediate use
export const logger = getLogger()

// Convenience exports for common logging patterns
export const log = {
  debug: (message: string, metadata?: any) => logger.debug(message, metadata),
  info: (message: string, metadata?: any) => logger.info(message, metadata),
  warn: (message: string, metadata?: any) => logger.warn(message, metadata),
  error: (message: string, error?: Error, metadata?: any) => logger.error(message, error, metadata),
  fatal: (message: string, error?: Error, metadata?: any) => logger.fatal(message, error, metadata),

  // Convenience methods
  api: (method: string, endpoint: string, statusCode: number, duration: number, metadata?: any) =>
    logger.apiRequest(method, endpoint, statusCode, duration, metadata),

  apiError: (method: string, endpoint: string, error: Error, metadata?: any) =>
    logger.apiError(method, endpoint, error, metadata),

  user: (action: string, userId: string, metadata?: any) =>
    logger.userAction(action, userId, metadata),

  business: (feature: string, action: string, entityType?: string, entityId?: string, metadata?: any) =>
    logger.businessLogic(feature, action, entityType, entityId, metadata),

  db: (operation: string, table: string, duration?: number, metadata?: any) =>
    logger.database(operation, table, duration, metadata),

  external: (service: string, operation: string, duration?: number, metadata?: any) =>
    logger.external(service, operation, duration, metadata)
}
