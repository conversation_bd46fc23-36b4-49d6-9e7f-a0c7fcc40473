
import { setupLogging, LogLevel } from './index'

export function initializeLogging() {
  const logger = setupLogging({
    level: (process.env.LOG_LEVEL as LogLevel) || LogLevel.INFO,
    enableConsole: process.env.NODE_ENV === 'development',
    enableStackTrace: process.env.NODE_ENV === 'development' || process.env.ENABLE_STACK_TRACE === 'true',
    defaultMetadata: {
      service: 'cs-ai-app',
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      hostname: process.env.HOSTNAME || 'unknown'
    }
  })

  // Log initialization
  logger.info('Logging system initialized', {
    level: process.env.LOG_LEVEL || LogLevel.INFO,
    grafanaEnabled: !!process.env.GRAFANA_LOKI_ENDPOINT,
    consoleEnabled: process.env.NODE_ENV === 'development',
    environment: process.env.NODE_ENV || 'development'
  })

  return logger
}

// Environment variables needed for Grafana integration
export const requiredEnvVars = {
  // Required for Grafana/Loki integration
  GRAFANA_LOKI_ENDPOINT: 'https://your-grafana-instance.com',

  // Authentication (choose one)
  GRAFANA_API_KEY: 'your-api-key',
  // OR
  GRAFANA_USERNAME: 'your-username',
  GRAFANA_PASSWORD: 'your-password',

  // Optional configuration
  LOG_LEVEL: 'info', // debug, info, warn, error, fatal
  OVERRIDE_CONSOLE: 'true', // Set to 'false' to disable console override
  ENABLE_STACK_TRACE: 'false' // Set to 'true' to enable stack traces in production
}

// Grafana Loki query examples for filtering logs
export const grafanaQueries = {
  // Filter by log level
  errorLogs: '{service="cs-ai-app"} |= "level" |= "error"',

  // Filter by component
  apiLogs: '{service="cs-ai-app", component="api"}',

  // Filter by user
  userLogs: '{service="cs-ai-app"} |= "userId" |= "user-123"',

  // Filter by endpoint
  specificEndpoint: '{service="cs-ai-app"} |= "endpoint" |= "/api/users"',

  // Performance issues
  slowRequests: '{service="cs-ai-app"} |= "duration" | json | duration > 1000',

  // Error patterns
  databaseErrors: '{service="cs-ai-app"} |= "error" |= "database"',

  // Business events
  userActions: '{service="cs-ai-app", component="user"}',

  // Time range with filters
  recentErrors: '{service="cs-ai-app", level="error"} [5m]'
}

// Dashboard configuration for Grafana
export const grafanaDashboardConfig = {
  panels: [
    {
      title: 'Log Volume by Level',
      type: 'stat',
      query: 'sum by (level) (count_over_time({service="cs-ai-app"}[5m]))'
    },
    {
      title: 'API Response Times',
      type: 'graph',
      query: 'histogram_quantile(0.95, sum(rate({service="cs-ai-app", component="api"} | json | __error__ = "" [5m])) by (le))'
    },
    {
      title: 'Error Rate',
      type: 'stat',
      query: 'sum(rate({service="cs-ai-app", level="error"}[5m]))'
    },
    {
      title: 'Recent Errors',
      type: 'logs',
      query: '{service="cs-ai-app", level="error"}'
    }
  ]
}
