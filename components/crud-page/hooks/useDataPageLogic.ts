"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { TableRowData } from "../types"
import { DEFAULT_PER_PAGE } from "../default_per_page"
import { toast } from "@/hooks/use-toast"
import { useLocalization } from "@/localization/functions/client"
import { apiLocales } from "@/app/api/locales"
import { deepMerge } from "@/lib/utils/deepMerge"
import { locales } from "../locales"

export interface DataPageEnhancedConfig<T = any> {
  title: string
  subtitle?: string
  headers: string[]
  action?: {
    edit: boolean
    delete: boolean
  }
  fetchData: (params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: Array<{ field: string; direction: "ASC" | "DESC" }>
    filters?: Array<{ field: string; operator?: string; value: any }>
  }) => Promise<{
    items: T[]
    total: number
    totalPages: number
  }>
  fetchStats?: () => Promise<any>
  deleteItem?: (id: string) => Promise<void>
  transformToTableRow: (item: T) => TableRowData
  addRoute: string
  editRoute: (id: string) => string
  bulkRoute?: string
  columnWidths?: Record<string, string>
  defaultColumnWidth?: string
  defaultPageSize?: number
  pinnedColumns?: string[]
}

export function useDataPageLogic<T>(config: DataPageEnhancedConfig<T>) {
  const router = useRouter()
  const { t } = useLocalization("crud-page", deepMerge(locales, apiLocales))

  const [listData, setListData] = useState<TableRowData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentFilters, setCurrentFilters] = useState({
    search: "",
    dateFilter: undefined as string | undefined,
    sort: undefined as { field: string; direction: "ASC" | "DESC" } | undefined,
    filters: undefined as { field: string; value: any }[] | undefined,
    sorts: undefined as
      | { field: string; direction: "ASC" | "DESC" }[]
      | undefined,
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(config.defaultPageSize || DEFAULT_PER_PAGE)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(1)
  const [pinnedColumns, setPinnedColumns] = useState<string[]>(
    config.pinnedColumns || [],
  )
  const [activeTab, setActiveTab] = useState("table")
  const [statsData, setStatsData] = useState<any>(null)

  const onAdd = () => router.push(config.addRoute)
  const onEdit = (id: string) => router.push(config.editRoute(id))
  const onBulk = () => config.bulkRoute && router.push(config.bulkRoute)

  const handleFiltersChange = (
    search: string,
    dateFilter?: string,
    sort?: { field: string; direction: "ASC" | "DESC" },
    filters?: { field: string; value: any }[],
    sorts?: { field: string; direction: "ASC" | "DESC" }[],
  ) => {
    setCurrentFilters({ search, dateFilter, sort, filters, sorts })
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => setCurrentPage(page)

  const handleDelete = async (id: string) => {
    if (!config.deleteItem) {
      toast({
        title: "Error",
        description: "Delete function not implemented",
        variant: "destructive",
      })
      return
    }

    try {
      await config.deleteItem(id)
      toast({
        title: "Success",
        description: "Item deleted successfully",
      })
      fetchData()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete item",
        variant: "destructive",
      })
    }
  }

  const handlePrint = () => window.print()

  const fetchData = async () => {
    try {
      setIsLoading(true)

      const combinedFilters: {
        field: string
        operator?: string
        value: any
      }[] = []
      const combinedSorts: { field: string; direction: "ASC" | "DESC" }[] = []

      if (currentFilters.filters?.length) {
        combinedFilters.push(...currentFilters.filters)
      }

      if (currentFilters.dateFilter) {
        combinedFilters.push({
          field: "date_range",
          value: currentFilters.dateFilter,
        })
      }

      if (currentFilters.sort) {
        combinedSorts.push(currentFilters.sort)
      }

      if (currentFilters.sorts?.length) {
        combinedSorts.push(...currentFilters.sorts)
      }

      const response = await config.fetchData({
        page: currentPage,
        limit: pageSize,
        search: currentFilters.search || undefined,
        filters: combinedFilters.length > 0 ? combinedFilters : undefined,
        sort: combinedSorts.length > 0 ? combinedSorts : undefined,
      })

      setListData(response.items.map(config.transformToTableRow))
      setTotalItems(response.total)
      setTotalPages(response.totalPages)
    } catch (error) {
      toast({
        title: "Error",
        description: t(`${error}`),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchStatsData = async () => {
    if (!config.fetchStats) return
    try {
      const stats = await config.fetchStats()
      setStatsData(stats)
    } catch (error) {
      console.error("Error fetching stats:", error)
    }
  }

  useEffect(() => {
    fetchStatsData()
  }, [])

  useEffect(() => {
    fetchData()
  }, [currentFilters, currentPage])

  return {
    listData,
    isLoading,
    currentFilters,
    currentPage,
    pageSize,
    totalItems,
    totalPages,
    pinnedColumns,
    setPinnedColumns,
    activeTab,
    setActiveTab,
    statsData,
    onAdd,
    onEdit,
    onBulk,
    handleFiltersChange,
    handlePageChange,
    handleDelete,
    handlePrint,
    fetchData,
    fetchStatsData,
  }
}
