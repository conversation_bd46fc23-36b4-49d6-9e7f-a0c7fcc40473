"use client"

import { locales } from "@/components/crm-dashboard/locales"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { SettingsAPI } from "@/lib/services/settingsApi"
import { useLocalization } from "@/localization/functions/client"
import {
  Bot,
  BotOff,
  Clock,
  Database,
  MessageSquare,
  RefreshCw,
  Search,
  Wifi,
  WifiOff
} from "lucide-react"
import { useState, useEffect } from "react"
import { toast } from "sonner"

interface SyncInfo {
  lastSync?: Date
  pendingChanges: number
  isOnline: boolean
}

interface ConversationListHeaderProps {
  searchQuery: string
  setSearchQuery: (query: string) => void
  selectedTags: string[]
  onTagsChange: (tags: string[]) => void
  showOfflineData: boolean
  toggleOfflineData: () => void
  lastSyncInfo: SyncInfo
  onRefresh?: () => void
  onSyncConversations?: () => void
  onSyncMessages?: () => void
  loading?: boolean
}

export function ConversationListHeader({
  searchQuery,
  setSearchQuery,
  selectedTags,
  onTagsChange,
  showOfflineData,
  toggleOfflineData,
  lastSyncInfo,
  onRefresh,
  onSyncConversations,
  onSyncMessages,
  loading = false,
}: ConversationListHeaderProps) {
  const { t } = useLocalization("crm-dashboard", locales)
  const [isAIEnabledByDefault, setIsAIEnabledByDefault] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadSetting = async () => {
      try {
        const result = await SettingsAPI.GetGlobalAISetting().request()
        setIsAIEnabledByDefault(result.enabled)
      } catch (error) {
        console.error("Error fetching global AI setting:", error)
        toast.error(t("loading_error"))
      } finally {
        setIsLoading(false)
      }
    }

    loadSetting()
  }, [])

  const toggleGlobalAI = async (enabled: boolean) => {
    const previousValue = isAIEnabledByDefault
    setIsAIEnabledByDefault(enabled)

    try {
      const result = await SettingsAPI.UpdateGlobalAISetting(enabled).request()
      toast.success(enabled ? t("ai_default_enabled") : t("ai_default_disabled"))
    } catch (error) {
      console.error("Error updating AI default:", error)
      toast.error(t("toggle_error"))
      setIsAIEnabledByDefault(previousValue) // Revert on failure
    }
  }

  const availableTags = [
    { key: "billing", label: t("conversation_list.tags.billing") },
    { key: "technical", label: t("conversation_list.tags.technical") },
    { key: "urgent", label: t("conversation_list.tags.urgent") },
    { key: "feature_request", label: t("conversation_list.tags.feature_request") },
    { key: "onboarding", label: t("conversation_list.tags.onboarding") },
    { key: "resolved", label: t("conversation_list.tags.resolved") },
  ]
  const handleTagChange = (tagKey: string, checked: boolean) => {
    if (checked) {
      onTagsChange([...selectedTags, tagKey])
    } else {
      onTagsChange(selectedTags.filter((t) => t !== tagKey))
    }
  }

  return (
    <div className="p-4 border-b">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-6 w-6" />
          <h1 className="text-xl font-semibold">{t("conversation_list.title")}</h1>
        </div>
      </div>

      {/* Sync Status and Data Controls */}
      <div hidden className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {/* <SyncStatusIndicator
              lastSync={lastSyncInfo.lastSync}
              pendingChanges={lastSyncInfo.pendingChanges}
              isOnline={lastSyncInfo.isOnline}
            /> */}
            <span className="text-sm text-gray-600">
              {showOfflineData ? t("conversation_list.sync_status.offline_data") : t("conversation_list.sync_status.live_data")}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={toggleOfflineData}
              className="h-7 px-2"
            >
              <Database className="h-3 w-3 mr-1" />
              {showOfflineData ? (
                <Wifi className="h-3 w-3" />
              ) : (
                <WifiOff className="h-3 w-3" />
              )}
            </Button>
            {onRefresh && (
              <Button
                size="sm"
                variant="outline"
                onClick={onRefresh}
                disabled={loading}
                className="h-7 px-2"
              >
                <RefreshCw
                  className={`h-3 w-3 ${loading ? "animate-spin" : ""}`}
                />
              </Button>
            )}
          </div>
        </div>

        {/* Sync Actions */}
        <div className="flex gap-2">
          {onSyncConversations && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onSyncConversations}
              className="h-6 px-2 text-xs"
            >
              {t("conversation_list.sync_status.sync_conversations")}
            </Button>
          )}
          {onSyncMessages && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onSyncMessages}
              className="h-6 px-2 text-xs"
            >
              {t("conversation_list.sync_status.sync_messages")}
            </Button>
          )}
        </div>

        {/* Last Sync Info */}
        {lastSyncInfo.lastSync && (
          <div className="flex items-center gap-1 mt-2 text-xs text-gray-500">
            <Clock className="h-3 w-3" />
            <span>{t("conversation_list.sync_status.last_sync", { time: lastSyncInfo.lastSync.toLocaleTimeString() })}</span>
          </div>
        )}
      </div>

      {/* Search */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          id="conversation-search"
          placeholder={t("conversation_list.search_placeholder")}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Global AI Toggle */}
      <div className="mb-4 p-3 bg-muted/30 rounded-lg border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
            ) : isAIEnabledByDefault ? (
              <Bot className="h-4 w-4 text-green-600" />
            ) : (
              <BotOff className="h-4 w-4 text-red-600" />
            )}
            <div className="flex flex-col">
              <span className="text-sm font-medium">
                {t("conversation_list.ai_toggle.label")}
              </span>
              <span className="text-xs text-muted-foreground">
                {isLoading
                  ? t("conversation_list.ai_toggle.loading")
                  : isAIEnabledByDefault
                    ? t("conversation_list.ai_toggle.status_enabled")
                    : t("conversation_list.ai_toggle.status_disabled")}
              </span>
            </div>
          </div>
          <Switch
            checked={isAIEnabledByDefault}
            onCheckedChange={toggleGlobalAI}
            disabled={isLoading}
          />
        </div>
      </div>

    </div>
  )
}
