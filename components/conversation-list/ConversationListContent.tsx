"use client"

import { useEffect, RefObject } from "react"
import { ConversationListItem } from "@/components/conversation-list-item"
import { Conversation } from "@/lib/repositories/conversations"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "@/components/crm-dashboard/locales"

interface ConversationListContentProps {
  conversations: Conversation[]
  selectedConversation: Conversation | null
  onSelectConversation: (conversation: Conversation) => void
  onEditConversation?: (conversation: Conversation) => void
  isCompact: boolean
  loading: boolean
  error: string | null
  listRef: RefObject<HTMLDivElement | null>
  page: number
  setPage: (page: number | ((prev: number) => number)) => void
  isLastConversations: boolean
  searchQuery: string
}

export function ConversationListContent({
  conversations,
  selectedConversation,
  onSelectConversation,
  onEditConversation,
  isCompact,
  loading,
  error,
  listRef,
  page,
  setPage,
  isLastConversations,
  searchQuery,
}: ConversationListContentProps) {
  const { t } = useLocalization("crm-dashboard", locales)
  // Handle infinite scroll
  useEffect(() => {
    const listEl = listRef.current
    if (!listEl || isLastConversations || loading) return

    const handleScroll = () => {
      if (
        listEl.scrollTop + listEl.clientHeight >= listEl.scrollHeight - 50 &&
        !loading &&
        !isLastConversations
      ) {
        setPage((prev) => prev + 1)
      }
    }

    listEl.addEventListener("scroll", handleScroll)

    return () => {
      listEl.removeEventListener("scroll", handleScroll)
    }
  }, [loading, isLastConversations, listRef, setPage])

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="text-center">
          <p className="text-red-600 mb-2">{t("conversation_list.states.error")}</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    )
  }

  if (loading && conversations.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p className="text-sm text-gray-500">{t("conversation_list.states.loading")}</p>
        </div>
      </div>
    )
  }

  if (conversations.length === 0 && !loading) {
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="text-center">
          <p className="text-gray-500 mb-2">{t("conversation_list.states.no_conversations")}</p>
          {searchQuery && (
            <p className="text-sm text-gray-400">
              {t("conversation_list.states.try_adjusting_search")}
            </p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div ref={listRef} className="flex-1 overflow-y-auto">
      <div className="space-y-1 p-2 py-4">
        {conversations.map((conversation) => (
          <ConversationListItem
            key={conversation.id}
            conversation={conversation}
            isSelected={selectedConversation?.id === conversation.id}
            onSelect={() => onSelectConversation(conversation)}
            onEdit={onEditConversation}
            isCompact={isCompact}
          />
        ))}

        {/* Loading indicator for pagination */}
        {loading && conversations.length > 0 && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
          </div>
        )}

        {/* End of list indicator */}
        {isLastConversations && conversations.length > 0 && (
          <div className="text-center py-4 text-sm text-gray-500">
            {isCompact ? "•••" : t("conversation_list.states.no_more_conversations")}
          </div>
        )}
      </div>
    </div>
  )
}
