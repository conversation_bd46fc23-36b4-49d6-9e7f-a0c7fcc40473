"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Play, X, ExternalLink } from "lucide-react"

interface YouTubeVideoProps {
  videoId: string
  title: string
  description?: string
  thumbnail?: string
  className?: string
  onDismiss?: () => void
  showDismiss?: boolean
}

export function YouTubeVideo({
  videoId,
  title,
  description,
  thumbnail,
  className = "",
  onDismiss,
  showDismiss = false
}: YouTubeVideoProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)

  const handleDismiss = () => {
    setIsDismissed(true)
    onDismiss?.()
  }

  if (isDismissed) return null

  const defaultThumbnail = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
  const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`
  const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <Play className="h-5 w-5 text-red-600" />
              {title}
            </CardTitle>
            {description && (
              <CardDescription className="mt-1">
                {description}
              </CardDescription>
            )}
          </div>
          {showDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="ml-2 h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
          {isPlaying ? (
            <iframe
              src={embedUrl}
              title={title}
              className="absolute inset-0 w-full h-full"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          ) : (
            <div 
              className="relative w-full h-full cursor-pointer group"
              onClick={() => setIsPlaying(true)}
            >
              <img
                src={thumbnail || defaultThumbnail}
                alt={title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to default thumbnail if custom thumbnail fails
                  const target = e.target as HTMLImageElement
                  if (target.src !== defaultThumbnail) {
                    target.src = defaultThumbnail
                  }
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group-hover:bg-opacity-40 transition-all">
                <div className="bg-red-600 rounded-full p-4 group-hover:scale-110 transition-transform">
                  <Play className="h-8 w-8 text-white ml-1" />
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="flex justify-between items-center mt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
          >
            <Play className="h-4 w-4 mr-2" />
            {isPlaying ? "Restart Video" : "Play Video"}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            asChild
          >
            <a href={youtubeUrl} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              Watch on YouTube
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
