import { AccountInfo } from "@/lib/repositories/account"
import React, { createContext, useContext, useState, ReactNode } from "react"

interface AccountInfoContextType {
    account: AccountInfo
}

const AccountInfoContext = createContext<AccountInfoContextType | undefined>(undefined)

export const useAccountInfoContext = (): AccountInfoContextType => {
    const context = useContext(AccountInfoContext)
    if (!context) {
        throw new Error("useAccountInfo must be used within an AccountInfoProvider")
    }
    return context
}

export const AccountInfoProvider = ({ children, account }: { children: ReactNode; account: AccountInfo }) => {
    return (
        <AccountInfoContext.Provider value={{ account }}>
            {children}
        </AccountInfoContext.Provider>
    )
}
