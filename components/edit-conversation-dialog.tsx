"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ConversationsAPI } from "@/lib/services/conversationApi"
import { Conversation } from "@/lib/repositories/conversations"
import { toast } from "sonner"
import { Loader2, X, Plus } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { editConversationDialogLocales } from "./edit-conversation-dialog/locales"

interface EditConversationDialogProps {
  conversation: Conversation
  open: boolean
  onOpenChange: (open: boolean) => void
  onConversationUpdated: (updatedConversation: Conversation) => void
}

export function EditConversationDialog({
  conversation,
  open,
  onOpenChange,
  onConversationUpdated,
}: EditConversationDialogProps) {
  const { t } = useLocalization("edit-conversation-dialog", editConversationDialogLocales)
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState("")
  const [loading, setLoading] = useState(false)

  // Initialize form with conversation data when dialog opens
  useEffect(() => {
    if (open && conversation) {
      setName(conversation.name || "")
      setDescription(conversation.description || "")
      setTags(conversation.tags || [])
      setNewTag("")
    }
  }, [open, conversation])

  // Tag management functions
  const addTag = () => {
    const trimmedTag = newTag.trim().toLowerCase()
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag])
      setNewTag("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }



  const handleSave = async () => {
    if (!name.trim()) {
      toast.error(t("form.validation.name_required"))
      return
    }

    try {
      setLoading(true)

      const response = await ConversationsAPI.Update(conversation.id, {
        name: name.trim(),
        description: description.trim() || undefined,
        tags: tags.length > 0 ? tags : undefined,
      }).request()

      if (response) {
        toast.success(t("messages.success"))
        onConversationUpdated(response)
        onOpenChange(false)
      }
    } catch (error: any) {
      console.error("Failed to update conversation:", error)
      const message = error.response?.data?.error || error.message || t("messages.generic_error")
      toast.error(message)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    // Reset form to original values
    setName(conversation.name || "")
    setDescription(conversation.description || "")
    setTags(conversation.tags || [])
    setNewTag("")
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("dialog.title")}</DialogTitle>
          <DialogDescription>
            {t("dialog.description")}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">
              {t("form.fields.name.label")} {t("form.fields.name.required")}
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder={t("form.fields.name.placeholder")}
              disabled={loading}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">{t("form.fields.description.label")}</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={t("form.fields.description.placeholder")}
              rows={3}
              disabled={loading}
            />
          </div>

          <div className="grid gap-2">
            <Label>{t("form.fields.tags.label")}</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeTag(tag)}
                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    disabled={loading}
                    title={t("actions.remove_tag")}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault()
                    addTag()
                  }
                }}
                placeholder={t("form.fields.tags.placeholder")}
                disabled={loading}
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addTag}
                disabled={loading || !newTag.trim()}
                title={t("form.fields.tags.add_button")}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            {t("actions.cancel")}
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={loading || !name.trim()}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? t("actions.saving") : t("actions.save")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
