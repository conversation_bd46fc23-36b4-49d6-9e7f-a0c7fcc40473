"use client"

import * as React from "react"
import { Check, ChevronDown, Globe, Languages } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useLocalizationContext } from "@/localization/functions/localization-context"

const languageOptions = [
  { 
    code: "id", 
    label: "Bahasa Indonesia", 
    nativeLabel: "Bahasa Indonesia",
    flag: "🇮🇩",
    region: "Indonesia"
  },
  { 
    code: "en", 
    label: "English", 
    nativeLabel: "English",
    flag: "🇺🇸",
    region: "United States"
  },
  { 
    code: "ja", 
    label: "Japanese", 
    nativeLabel: "日本語",
    flag: "🇯🇵",
    region: "Japan"
  },
  { 
    code: "ar", 
    label: "Arabic", 
    nativeLabel: "العربية",
    flag: "🇸🇦",
    region: "Saudi Arabia"
  },
]

interface LanguageSelectorProps {
  variant?: "default" | "compact" | "minimal"
  className?: string
  showLabel?: boolean
  align?: "start" | "center" | "end"
}

export function LanguageSelector({ 
  variant = "default", 
  className,
  showLabel = false,
  align = "end"
}: LanguageSelectorProps) {
  const { locale, setLocale } = useLocalizationContext()
  const currentLanguage = languageOptions.find((lang) => lang.code === locale) || languageOptions[0]

  const handleLanguageChange = (languageCode: string) => {
    if (languageCode !== locale) {
      setLocale(languageCode)
    }
  }

  if (variant === "minimal") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-8 w-8 p-0 hover:bg-gray-100 transition-colors",
              className
            )}
          >
            <span className="text-lg">{currentLanguage.flag}</span>
            <span className="sr-only">Select language</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={align} className="w-56">
          {languageOptions.map((language) => (
            <DropdownMenuItem
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={cn(
                "flex items-center gap-3 px-3 py-2 cursor-pointer",
                language.code === locale && "bg-gray-50"
              )}
            >
              <span className="text-lg">{language.flag}</span>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{language.nativeLabel}</span>
                <span className="text-xs text-gray-500">{language.region}</span>
              </div>
              {language.code === locale && (
                <Check className="ml-auto h-4 w-4 text-blue-600" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  if (variant === "compact") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "h-9 px-3 gap-2 border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200",
              className
            )}
          >
            <span className="text-base">{currentLanguage.flag}</span>
            {showLabel && (
              <span className="text-sm font-medium hidden sm:inline">
                {currentLanguage.code.toUpperCase()}
              </span>
            )}
            <ChevronDown className="h-3 w-3 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={align} className="w-64 p-1">
          <div className="px-2 py-1.5 text-xs font-medium text-gray-500 uppercase tracking-wide">
            Select Language
          </div>
          {languageOptions.map((language) => (
            <DropdownMenuItem
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={cn(
                "flex items-center gap-3 px-3 py-2.5 cursor-pointer rounded-md mx-1",
                "hover:bg-blue-50 hover:text-blue-700 transition-colors",
                language.code === locale && "bg-blue-100 text-blue-700"
              )}
            >
              <span className="text-lg">{language.flag}</span>
              <div className="flex flex-col flex-1">
                <span className="text-sm font-medium">{language.nativeLabel}</span>
                <span className="text-xs text-gray-500">{language.region}</span>
              </div>
              {language.code === locale && (
                <Check className="h-4 w-4 text-blue-600" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // Default variant
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "h-10 px-4 gap-3 border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200",
            "focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            className
          )}
        >
          <Globe className="h-4 w-4 text-gray-600" />
          <div className="flex items-center gap-2">
            <span className="text-base">{currentLanguage.flag}</span>
            <span className="text-sm font-medium">{currentLanguage.nativeLabel}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align} className="w-72 p-2">
        <div className="flex items-center gap-2 px-3 py-2 mb-2 bg-gray-50 rounded-md">
          <Languages className="h-4 w-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-700">Choose Language</span>
        </div>
        {languageOptions.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={cn(
              "flex items-center gap-3 px-3 py-3 cursor-pointer rounded-lg mx-1",
              "hover:bg-blue-50 hover:text-blue-700 transition-all duration-200",
              language.code === locale && "bg-blue-100 text-blue-700 shadow-sm"
            )}
          >
            <span className="text-xl">{language.flag}</span>
            <div className="flex flex-col flex-1">
              <span className="text-sm font-semibold">{language.nativeLabel}</span>
              <span className="text-xs text-gray-500">{language.label} • {language.region}</span>
            </div>
            {language.code === locale && (
              <div className="flex items-center justify-center w-6 h-6 bg-blue-600 rounded-full">
                <Check className="h-3 w-3 text-white" />
              </div>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
