"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Send, Bot, User, Clock, Zap, MessageSquare } from "lucide-react"
import { TestConversation, TestMessage } from "@/lib/repositories/testConversations"
import { cn } from "@/lib/utils"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface TestChatInterfaceProps {
  conversation: TestConversation
  onSendMessage: (message: string) => Promise<void>
  loading: boolean
}

export function TestChatInterface({
  conversation,
  onSendMessage,
  loading
}: TestChatInterfaceProps) {
  const [message, setMessage] = useState("")
  const [sending, setSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const { t } = useLocalization("test-chat", locales)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [conversation.messages])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [message])

  const handleSendMessage = async () => {
    if (!message.trim() || sending) return

    setSending(true)
    try {
      await onSendMessage(message.trim())
      setMessage("")
    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      setSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const getMessageIcon = (message: TestMessage) => {
    if (message.isFromAI) {
      return <Bot className="h-4 w-4 text-blue-600" />
    } else if (message.isFromCustomer) {
      return <User className="h-4 w-4 text-green-600" />
    } else {
      return <MessageSquare className="h-4 w-4 text-gray-500" />
    }
  }

  const getMessageBadge = (message: TestMessage) => {
    if (message.isFromAI) {
      return (
        <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs">
          <Bot className="h-3 w-3 mr-1" />
          AI Assistant
        </Badge>
      )
    } else if (message.isFromCustomer) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs">
          <User className="h-3 w-3 mr-1" />
          Customer
        </Badge>
      )
    } else {
      return (
        <Badge variant="outline" className="text-xs">
          System
        </Badge>
      )
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const scenarioTitle = t(`scenarios.${conversation.scenario}.title`)
  const scenarioDescription = t(`scenarios.${conversation.scenario}.description`)
  const scenarioSamples = conversation.sampleMessages;

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="px-6 py-4 border-b bg-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-lg">{conversation.customerName}</h3>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-muted-foreground">
                {conversation.customerPhone}
              </span>
              <Badge variant="outline" className="text-xs">
                {scenarioTitle}
              </Badge>
              <Badge
                variant={conversation.status === 'OPEN' ? 'default' : 'secondary'}
                className="text-xs"
              >
                {conversation.status}
              </Badge>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted-foreground">
              {conversation.messages.length} messages
            </div>
            <div className="text-xs text-muted-foreground">
              AI: {conversation.isAiEnabled ? 'Enabled' : 'Disabled'}
            </div>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
        {/* Scenario Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageSquare className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-blue-900 mb-1">{t('testScenario')}: {scenarioTitle}</h4>
              <p className="text-sm text-blue-700 mb-2">{scenarioDescription}</p>
              <div className="text-xs text-blue-600">
                <strong>{t('sampleMessages')}:</strong> {scenarioSamples.slice(0, 2).join(', ')}
              </div>
            </div>
          </div>
        </div>

        {/* Messages */}
        {conversation.messages.map((msg) => (
          <div key={msg.id} className="space-y-2">
            <div className={cn(
              "flex gap-3",
              msg.isFromCustomer ? "justify-end" : "justify-start"
            )}>
              {!msg.isFromCustomer && (
                <div className="w-8 h-8 bg-white border rounded-full flex items-center justify-center">
                  {getMessageIcon(msg)}
                </div>
              )}

              <div className={cn(
                "max-w-[70%] space-y-1",
                msg.isFromCustomer ? "items-end" : "items-start"
              )}>
                <div className="flex items-center gap-2">
                  {getMessageBadge(msg)}
                  <span className="text-xs text-muted-foreground">
                    {formatTimestamp(msg.timestamp)}
                  </span>
                  {(msg.metadata as any)?.processingTime && (
                    <Badge variant="outline" className="text-xs">
                      <Clock className="h-3 w-3 mr-1" />
                      {(msg.metadata as any).processingTime}ms
                    </Badge>
                  )}
                </div>

                <div className={cn(
                  "rounded-lg p-3 break-words",
                  msg.isFromCustomer
                    ? "bg-blue-600 text-white"
                    : msg.isFromAI
                      ? "bg-white border shadow-sm"
                      : "bg-gray-100 text-gray-700"
                )}>
                  <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                </div>

                {(msg.metadata as any)?.confidence && (
                  <div className="text-xs text-muted-foreground">
                    Confidence: {Math.round((msg.metadata as any).confidence * 100)}%
                  </div>
                )}
              </div>

              {msg.isFromCustomer && (
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">
                  <User className="h-4 w-4" />
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Loading indicator */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">Loading messages...</p>
          </div>
        )}
        {(sending) && (
          <div className="flex gap-3">
            <div className="w-8 h-8 bg-white border rounded-full flex items-center justify-center">
              <Bot className="h-4 w-4 text-blue-600 animate-pulse" />
            </div>
            <div className="bg-white border rounded-lg p-3 shadow-sm">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                AI is thinking...
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t bg-white">
        <div className="flex gap-3">
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              placeholder={t('placeholders.typeMessage')}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              className="min-h-[44px] max-h-32 resize-none"
              rows={1}
            />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!message.trim() || sending}
            className="self-end"
          >
            {sending ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>

        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <div className="flex items-center gap-4">
            <span>{t('testScenario')}: {scenarioTitle}</span>
            <span className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              AI {conversation.isAiEnabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
