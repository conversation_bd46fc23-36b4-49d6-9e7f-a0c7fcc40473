"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { TestConversation, TestScenario } from "@/lib/repositories/testConversations"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface ConversationEditDialogProps {
  isOpen: boolean
  onClose: () => void
  conversation?: TestConversation | null
  onSave: (data: ConversationFormData) => Promise<void>
  loading?: boolean
}

export interface ConversationFormData {
  title: string
  customerName: string
  customerPhone: string
  scenario: TestScenario
  scenarioDescription: string
  sampleMessages: string[]
}

const SCENARIO_OPTIONS: TestScenario[] = [
  'general_inquiry',
  'product_support',
  'billing_issue',
  'technical_problem',
  'complaint',
  'compliment',
  'order_status',
  'refund_request',
  'custom'
]

export function ConversationEditDialog({
  isOpen,
  onClose,
  conversation,
  onSave,
  loading = false
}: ConversationEditDialogProps) {
  const { t, locale } = useLocalization("test-chat", locales)
  const isEditing = !!conversation

  const [formData, setFormData] = useState<ConversationFormData>({
    title: '',
    customerName: '',
    customerPhone: '',
    scenario: 'general_inquiry',
    scenarioDescription: '',
    sampleMessages: []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Initialize form data when dialog opens or conversation changes
  useEffect(() => {
    if (isOpen) {
      if (conversation) {
        // Editing existing conversation
        setFormData({
          title: conversation.title || '',
          customerName: conversation.customerName,
          customerPhone: conversation.customerPhone,
          scenario: conversation.scenario,
          scenarioDescription: conversation.scenarioDescription || t(`scenarios.${conversation.scenario}.description`),
          sampleMessages: conversation.sampleMessages || (((locales as any)[locale].scenarios as any))[conversation.scenario].samples
        })
      } else {
        // Creating new conversation
        setFormData({
          title: '',
          customerName: '',
          customerPhone: `+1555${Math.floor(Math.random() * 9000000) + 1000000}`,
          scenario: 'general_inquiry',
          scenarioDescription: t('scenarios.general_inquiry.description'),
          sampleMessages: ((locales as any)[locale].scenarios as any).general_inquiry.samples
        })
      }
      setErrors({})
    }
  }, [conversation, isOpen])

  // Update scenario description and samples when scenario changes
  const handleScenarioChange = (scenario: TestScenario) => {
    setFormData(prev => ({
      ...prev,
      scenario,
      scenarioDescription: t(`scenarios.${scenario}.description`),
      sampleMessages: ((locales as any)[locale].scenarios as any)[scenario].samples
    }))
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = t('validation.titleRequired')
    }
    if (!formData.customerName.trim()) {
      newErrors.customerName = t('validation.customerNameRequired')
    }
    if (!formData.customerPhone.trim()) {
      newErrors.customerPhone = t('validation.phoneRequired')
    }
    if (!formData.scenario) {
      newErrors.scenario = t('validation.scenarioRequired')
    }
    if (!formData.scenarioDescription.trim()) {
      newErrors.scenarioDescription = t('validation.descriptionRequired')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Failed to save conversation:', error)
    }
  }

  const handleSampleMessagesChange = (value: string) => {
    const messages = value.split('\n').filter(msg => msg.trim())
    setFormData(prev => ({ ...prev, sampleMessages: messages }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? t('editConversation') : t('createConversation')}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Conversation Title */}
          <div className="space-y-2">
            <Label htmlFor="title">{t('conversationTitle')}</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder={t('placeholders.conversationTitle')}
              className={errors.title ? "border-red-500" : ""}
            />
            {errors.title && (
              <p className="text-sm text-red-500">{errors.title}</p>
            )}
          </div>

          {/* Customer Name */}
          <div className="space-y-2">
            <Label htmlFor="customerName">{t('customerName')}</Label>
            <Input
              id="customerName"
              value={formData.customerName}
              onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
              placeholder={t('placeholders.customerName')}
              className={errors.customerName ? "border-red-500" : ""}
            />
            {errors.customerName && (
              <p className="text-sm text-red-500">{errors.customerName}</p>
            )}
          </div>

          {/* Customer Phone */}
          <div className="space-y-2">
            <Label htmlFor="customerPhone">{t('customerPhone')}</Label>
            <Input
              id="customerPhone"
              value={formData.customerPhone}
              onChange={(e) => setFormData(prev => ({ ...prev, customerPhone: e.target.value }))}
              placeholder={t('placeholders.customerPhone')}
              className={errors.customerPhone ? "border-red-500" : ""}
            />
            {errors.customerPhone && (
              <p className="text-sm text-red-500">{errors.customerPhone}</p>
            )}
          </div>

          {/* Test Scenario */}
          <div className="space-y-2">
            <Label htmlFor="scenario">{t('testScenario')}</Label>
            <Select
              value={formData.scenario}
              onValueChange={handleScenarioChange}
            >
              <SelectTrigger className={errors.scenario ? "border-red-500" : ""}>
                <SelectValue placeholder="Select scenario..." />
              </SelectTrigger>
              <SelectContent>
                {SCENARIO_OPTIONS.map((scenario) => {
                  return (
                    <SelectItem key={scenario} value={scenario}>
                      {t(`scenarios.${scenario}.title`)}
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
            {errors.scenario && (
              <p className="text-sm text-red-500">{errors.scenario}</p>
            )}
          </div>

          {/* Scenario Description */}
          <div className="space-y-2">
            <Label htmlFor="scenarioDescription">{t('scenarioDescription')}</Label>
            <Textarea
              id="scenarioDescription"
              value={formData.scenarioDescription}
              onChange={(e) => setFormData(prev => ({ ...prev, scenarioDescription: e.target.value }))}
              placeholder={t('placeholders.scenarioDescription')}
              rows={3}
              className={errors.scenarioDescription ? "border-red-500" : ""}
            />
            {errors.scenarioDescription && (
              <p className="text-sm text-red-500">{errors.scenarioDescription}</p>
            )}
          </div>

          {/* Sample Messages */}
          <div className="space-y-2">
            <Label htmlFor="sampleMessages">{t('sampleMessages')}</Label>
            <Textarea
              id="sampleMessages"
              value={formData.sampleMessages.join('\n')}
              onChange={(e) => handleSampleMessagesChange(e.target.value)}
              placeholder={t('placeholders.sampleMessages')}
              rows={4}
            />
            <p className="text-sm text-muted-foreground">
              Enter one message per line
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? t('loading') : (isEditing ? t('save') : t('create'))}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
