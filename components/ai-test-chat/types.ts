import { TestConversation, TestMessage, TestScenario } from "@/lib/repositories/testConversations"

export interface CreateTestConversationRequest {
  customerName: string
  customerPhone: string
  scenario: TestScenario
  scenarioDescription: string
  sampleMessages: string[]
  title: string
}

export interface SendTestMessageRequest {
  conversationId: string
  message: string
  metadata?: {
    scenario?: TestScenario
    customerPersonality?: string
    simulateDelay?: boolean
  }
}

export interface TestWebhookPayload {
  id: string
  timestamp: number
  event: "message"
  session: string
  metadata: Record<string, any>
  me: {
    id: string
    pushName: string
  }
  payload: {
    id: string
    timestamp: number
    from: string
    fromMe: false
    source: string
    body: string
    hasMedia: false
    media: null
    ack: number
    ackName: string
    replyTo: null
    _data: {
      key: {
        remoteJid: string
        fromMe: false
        id: string
      }
      messageTimestamp: number
      pushName: string
      broadcast: false
      message: {
        conversation: string
      }
      status: number
    }
  }
  engine: "WEBJS"
  environment: {
    version: "2024.1.1"
    engine: "WEBJS"
    tier: "PLUS"
    browser: null
  }
}

export interface TestChatAPI {
  createConversation: (request: CreateTestConversationRequest) => Promise<TestConversation>
  getConversations: () => Promise<TestConversation[]>
  getConversation: (id: string) => Promise<TestConversation>
  sendMessage: (request: SendTestMessageRequest) => Promise<TestMessage>
  deleteConversation: (id: string) => Promise<void>
  clearConversation: (id: string) => Promise<void>
  updateSettings: (conversationId: string) => Promise<void>
}
