"use client"

import { useState, useEffect, useCallback } from "react"
import { CreateTestConversationRequest, TestWebhookPayload } from "../types"
import { TestConversation, TestMessage } from "@/lib/repositories/testConversations"
import { TestConversationsAPI } from "@/lib/services"

export function useTestChat() {
  const [conversations, setConversations] = useState<TestConversation[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load conversations from API and localStorage on mount
  useEffect(() => {
    loadConversations()
  }, [])

  const loadConversations = useCallback(async () => {
    try {
      setLoading(true)
      const result = await TestConversationsAPI.All({
        page: 1,
        per_page: 100
      }).request()

      setConversations(result.items)
      return
    } catch (err) {
      console.error('Failed to load conversations from API:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Save conversations to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('test-chat-conversations', JSON.stringify(conversations))
  }, [conversations])

  const createConversation = useCallback(async (request: CreateTestConversationRequest): Promise<TestConversation> => {
    setLoading(true)
    setError(null)

    try {
      const testPhone = `+1555${Math.floor(Math.random() * 9000000) + 1000000}`

      const newConversation = await TestConversationsAPI.Create({
        customerName: request.customerName,
        customerPhone: testPhone,
        scenario: request.scenario,
        scenarioDescription: request.scenarioDescription,
        sampleMessages: request.sampleMessages,
        title: request.title
      }).request()

      setConversations(prev => [newConversation, ...prev])
      return newConversation
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create conversation'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const sendMessage = useCallback(async (conversationId: string, message: string): Promise<TestMessage> => {
    setError(null)

    try {
      // Find the conversation
      const conversation = conversations.find(c => c.id === conversationId)
      if (!conversation) {
        throw new Error('Conversation not found')
      }

      // Create customer message immediately
      const customerMessage: TestMessage = {
        id: `msg_${Date.now()}_customer`,
        content: message,
        senderId: 'test_customer',
        senderName: conversation.customerName,
        isFromCustomer: true,
        isFromAI: false,
        timestamp: new Date().toISOString(),
        messageType: 'TEXT'
      }

      // Update conversation with customer message
      setConversations(prev => prev.map(c =>
        c.id === conversationId
          ? { ...c, messages: [...c.messages, customerMessage] }
          : c
      ))

      const webhookPayload: TestWebhookPayload = {
        id: `webhook_${Date.now()}`,
        timestamp: Date.now(),
        event: "message",
        session: "test-session-id_" + conversationId,
        metadata: {
          INTERNAL_TESTING: true,
          testScenario: conversation.scenario,
          testConversationId: conversationId
        },
        me: {
          id: "test-session-id_" + conversationId,
          pushName: conversation.customerName
        },
        payload: {
          id: customerMessage.id,
          timestamp: Date.now(),
          from: `${conversation.customerPhone.replace('+', '')}@c.us`,
          fromMe: false,
          source: "test",
          body: message,
          hasMedia: false,
          media: null,
          ack: 1,
          ackName: "sent",
          replyTo: null,
          _data: {
            key: {
              remoteJid: `${conversation.customerPhone.replace('+', '')}@c.us`,
              fromMe: false,
              id: customerMessage.id
            },
            messageTimestamp: Date.now(),
            pushName: conversation.customerName,
            broadcast: false,
            message: {
              conversation: message
            },
            status: 1
          }
        },
        engine: "WEBJS",
        environment: {
          version: "2024.1.1",
          engine: "WEBJS",
          tier: "PLUS",
          browser: null
        }
      }

      await TestConversationsAPI.SendMessage(conversationId, webhookPayload).request()

      return customerMessage
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message'
      setError(errorMessage)
      throw err
    } finally {
    }
  }, [conversations])

  const deleteConversation = useCallback(async (conversationId: string): Promise<void> => {
    setLoading(true)
    setError(null)
    try {
      await TestConversationsAPI.Delete(conversationId).request()
      setConversations(prev => prev.filter(c => c.id !== conversationId))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete conversation'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const clearConversation = useCallback(async (conversationId: string): Promise<void> => {
    setLoading(true)
    setError(null)
    try {
      // Check if it's a real conversation or local test conversation
      if (conversationId.startsWith('test_conv_')) {
        // Local test conversation - clear messages in state only
        setConversations(prev => prev.map(c =>
          c.id === conversationId
            ? { ...c, messages: [] }
            : c
        ))
      } else {
        // Real conversation - clear via API
        await TestConversationsAPI.Clear(conversationId).request()

        // Clear messages in local state
        setConversations(prev => prev.map(c =>
          c.id === conversationId
            ? { ...c, messages: [] }
            : c
        ))
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear conversation'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  // Add AI response via API
  const addAIResponse = useCallback(async (conversationId: string, aiMessage: TestMessage) => {
    try {

      // Check if it's a real conversation or local test conversation
      if (conversationId.startsWith('test_conv_')) {
        // Local test conversation - add to state only
        setConversations(prev => prev.map(c =>
          c.id === conversationId
            ? { ...c, messages: [...c.messages, aiMessage] }
            : c
        ))
      } else {
        // Real conversation - add via API
        const result = await TestConversationsAPI.AddMessage(conversationId, aiMessage).request()

        // Update local state with the response from API
        setConversations(prev => prev.map(c =>
          c.id === conversationId
            ? { ...c, messages: [...c.messages, result.message] }
            : c
        ))
      }
    } catch (err) {
      console.error('Failed to add AI response:', err)
    }
  }, [])

  return {
    conversations,
    loading,
    error,
    createConversation,
    sendMessage,
    deleteConversation,
    clearConversation,
    addAIResponse,
    loadConversations
  }
}
