"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON> } from "lucide-react"
import { TestChatOverlay } from "./TestChatOverlay"
import { cn } from "@/lib/utils"

interface TestChatFABProps {
  className?: string
  isCompact?: boolean
  label?: string
}

export function TestChatFAB({
  className,
  isCompact = false,
  label = "Test Chat",
}: TestChatFABProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <div className={cn("w-full", className)} title={isCompact ? label : undefined}>
        <Button
          onClick={() => setIsOpen(true)}
          variant={"default"}
          className={cn(
            "flex items-center gap-2 rounded px-3 py-2 text-sm font-medium transition-all w-full",
            "bg-purple-600 text-white hover:bg-purple-700",
            isCompact && "justify-center px-2"
          )}
        >
          <Bot className="w-5 h-5" />
          {!isCompact && <span>{label}</span>}
        </Button>
      </div>

      <TestChatOverlay isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  )
}
