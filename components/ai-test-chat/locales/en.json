{"title": "AI Test Chat", "subtitle": "Test your AI assistant with simulated customer conversations", "newConversation": "New Test Conversation", "settings": "Settings", "noConversations": "No test conversations yet", "createFirstConversation": "Create your first test conversation to start testing the AI assistant.", "conversationTitle": "Conversation Title", "customerName": "Customer Name", "customerPhone": "Customer Phone", "testScenario": "<PERSON>enario", "scenarioDescription": "Scenario Description", "sampleMessages": "Sample Messages", "create": "Create", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete Conversation", "clear": "Clear Messages", "close": "Close", "loading": "Loading...", "sending": "Sending...", "error": "Error", "success": "Success", "confirmDelete": "Are you sure you want to delete this conversation?", "confirmClear": "Are you sure you want to clear all messages?", "conversationCreated": "Test conversation created successfully", "conversationUpdated": "Conversation updated successfully", "conversationDeleted": "Conversation deleted successfully", "messagesCleared": "Messages cleared successfully", "editConversation": "Edit Conversation", "createConversation": "Create Test Conversation", "scenarios": {"general_inquiry": {"title": "General Inquiry", "description": "Basic questions about products or services", "samples": ["Hi, I have a question about your services", "Can you tell me more about your pricing?", "What products do you offer?"]}, "product_support": {"title": "Product Support", "description": "Help with using products or features", "samples": ["I'm having trouble with your product", "How do I use this feature?", "The product isn't working as expected"]}, "billing_issue": {"title": "Billing Issue", "description": "Questions about payments and billing", "samples": ["I have a question about my bill", "There's a charge I don't recognize", "Can you help me with payment issues?"]}, "technical_problem": {"title": "Technical Problem", "description": "Technical issues and troubleshooting", "samples": ["The website isn't loading properly", "I'm getting an error message", "Something seems to be broken"]}, "complaint": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Customer complaints and negative feedback", "samples": ["I'm not satisfied with the service", "This is unacceptable", "I want to file a complaint"]}, "compliment": {"title": "Compliment", "description": "Positive feedback and praise", "samples": ["Great service, thank you!", "I'm very happy with your product", "Excellent customer support"]}, "order_status": {"title": "Order Status", "description": "Questions about order tracking and delivery", "samples": ["Where is my order?", "Can you check my delivery status?", "When will my package arrive?"]}, "refund_request": {"title": "Refund Request", "description": "Requests for refunds and returns", "samples": ["I'd like to return this product", "Can I get a refund?", "How do I process a return?"]}, "custom": {"title": "Custom Scenario", "description": "Create your own test scenario", "samples": ["Type your custom message here", "Add your own test scenarios", "Customize the conversation flow"]}}, "placeholders": {"conversationTitle": "Enter conversation title...", "customerName": "Enter customer name...", "customerPhone": "Enter phone number...", "scenarioDescription": "Describe what this scenario tests...", "sampleMessages": "Enter sample messages (one per line)...", "typeMessage": "Type your message..."}, "validation": {"titleRequired": "Conversation title is required", "customerNameRequired": "Customer name is required", "phoneRequired": "Phone number is required", "scenarioRequired": "Test scenario is required", "descriptionRequired": "Scenario description is required"}}