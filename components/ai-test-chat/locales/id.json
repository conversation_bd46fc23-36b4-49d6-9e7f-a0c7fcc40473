{"title": "Uji Coba Chat AI", "subtitle": "Uji asisten AI Anda dengan simulasi percakapan pelanggan", "newConversation": "Percaka<PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON>", "noConversations": "Belum ada perca<PERSON>pan uji coba", "createFirstConversation": "Buat percakapan uji pertama Anda untuk mulai menguji asisten AI.", "conversationTitle": "<PERSON><PERSON><PERSON>", "customerName": "<PERSON><PERSON>", "customerPhone": "Telepon Pelanggan", "testScenario": "<PERSON><PERSON><PERSON>", "scenarioDescription": "Desk<PERSON><PERSON>", "sampleMessages": "<PERSON><PERSON><PERSON>", "create": "Buat", "save": "Simpan", "cancel": "<PERSON><PERSON>", "edit": "Edit", "delete": "<PERSON><PERSON>", "clear": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "loading": "Memuat...", "sending": "Mengirim...", "error": "Error", "success": "<PERSON><PERSON><PERSON><PERSON>", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus percakapan ini?", "confirmClear": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus semua pesan?", "conversationCreated": "Percakapan uji berhasil dibuat", "conversationUpdated": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "conversationDeleted": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "messagesCleared": "<PERSON><PERSON> be<PERSON>", "editConversation": "<PERSON>", "createConversation": "Buat Percakapan <PERSON>", "scenarios": {"general_inquiry": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> dasar tentang produk atau layanan", "samples": ["<PERSON><PERSON>, saya ada pertanyaan tentang layanan Anda", "<PERSON><PERSON><PERSON><PERSON> Anda ceritakan lebih lanjut tentang harga?", "Produk apa saja yang <PERSON>a ta<PERSON>?"]}, "product_support": {"title": "Dukungan Produk", "description": "Bantuan menggunakan produk atau fitur", "samples": ["<PERSON><PERSON> mengalami masalah dengan produk <PERSON>a", "Bagaimana cara menggunakan fitur ini?", "Produk tidak berfungsi seperti yang diharapkan"]}, "billing_issue": {"title": "<PERSON><PERSON><PERSON>", "description": "Pertanyaan tentang pembayaran dan tagihan", "samples": ["Saya ada pertanyaan tentang tagihan saya", "<PERSON> tagihan yang tidak saya kenali", "<PERSON><PERSON><PERSON><PERSON> Anda bantu dengan masalah pembayaran?"]}, "technical_problem": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> teknis dan pem<PERSON>han masalah", "samples": ["Website tidak memuat dengan benar", "<PERSON><PERSON> menda<PERSON>t pesan error", "<PERSON><PERSON><PERSON>ya ada yang rusak"]}, "complaint": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> pelanggan dan umpan balik negatif", "samples": ["<PERSON>a tidak puas dengan layanannya", "Ini tidak dapat diterima", "<PERSON>a ingin menga<PERSON> keluhan"]}, "compliment": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> balik positif dan pujian", "samples": ["<PERSON><PERSON>n yang bagus, terima kasih!", "<PERSON>a sangat senang dengan produk Anda", "Dukungan pelanggan yang luar biasa"]}, "order_status": {"title": "<PERSON> Pesanan", "description": "Pertanyaan tentang pelacakan pesanan dan pen<PERSON>man", "samples": ["Di mana pesanan saya?", "<PERSON><PERSON><PERSON><PERSON>a cek status pengiriman saya?", "<PERSON><PERSON> paket saya akan tiba?"]}, "refund_request": {"title": "Permintaan Refund", "description": "Permin<PERSON>an pengembalian dana dan retur", "samples": ["<PERSON>a ingin men<PERSON>an produk ini", "<PERSON><PERSON><PERSON><PERSON> saya mendapat refund?", "Bagaimana cara memproses pengembalian?"]}, "custom": {"title": "<PERSON><PERSON><PERSON>", "description": "Buat skenario uji Anda sendiri", "samples": ["Ketik pesan kustom Anda di sini", "Tambahkan skenario uji Anda sendiri", "<PERSON><PERSON><PERSON><PERSON> alur <PERSON>"]}}, "placeholders": {"conversationTitle": "Masukkan judul percakapan...", "customerName": "<PERSON><PERSON><PERSON>n nama pelanggan...", "customerPhone": "Masukkan nomor telepon...", "scenarioDescription": "Jelaskan apa yang diuji skenario ini...", "sampleMessages": "<PERSON><PERSON><PERSON><PERSON> contoh pesan (satu per baris)...", "typeMessage": "<PERSON><PERSON>k pesan Anda..."}, "validation": {"titleRequired": "<PERSON><PERSON><PERSON> wajib <PERSON>isi", "customerNameRequired": "<PERSON><PERSON> p<PERSON> wajib diisi", "phoneRequired": "Nomor telepon wajib diisi", "scenarioRequired": "<PERSON><PERSON><PERSON> uji wajib diisi", "descriptionRequired": "Deskripsi skenario wajib diisi"}}