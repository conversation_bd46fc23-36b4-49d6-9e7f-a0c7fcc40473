"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"

import { MessageCircle, X, Trash2, <PERSON><PERSON>, Edit, Plus } from "lucide-react"
import { useTestChat } from "./hooks/useTestChat"
import { TestChatInterface } from "./TestChatInterface"
import { ConversationEditDialog, ConversationFormData } from "./ConversationEditDialog"
import { TestConversation, TestMessage } from "@/lib/repositories/testConversations"
import { RealtimeTestConversationRoom } from "@/lib/realtime/model"
import { TestConversationsAPI } from "@/lib/services"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface TestChatOverlayProps {
  isOpen: boolean
  onClose: () => void
}

export function TestChatOverlay({ isOpen, onClose }: TestChatO<PERSON>layProps) {

  const [currentConversation, setCurrentConversation] = useState<TestConversation | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingConversation, setEditingConversation] = useState<TestConversation | null>(null)

  const { t } = useLocalization("test-chat", locales)

  const {
    conversations,
    loading,
    error,
    createConversation,
    deleteConversation,
    sendMessage,
    clearConversation,
    addAIResponse
  } = useTestChat()

  useEffect(() => {
    if (conversations.length > 0 && !currentConversation) {
      setCurrentConversation(conversations[0])
    }
  }, [conversations, currentConversation])

  const handleNewMessage = async (conversationId: string, message: TestMessage) => {
    await addAIResponse(conversationId, message)
    if (conversationId !== currentConversation?.id) return
    setCurrentConversation(prev => {
      if (!prev) return prev
      return {
        ...prev,
        messages: [...prev.messages, message]
      }
    })
  }

  useEffect(() => {
    if (!currentConversation) return
    const channel = RealtimeTestConversationRoom.subscribe(currentConversation.id || '')
    RealtimeTestConversationRoom.LISTEN_NEW_MESSAGE(channel, (conversationId, message) => {
      handleNewMessage(conversationId, message)
    })
    return () => {
      channel.unbind_all()
      channel.unsubscribe()
    }
  }, [currentConversation])

  const handleCreateConversation = () => {
    setEditingConversation(null)
    setShowEditDialog(true)
  }

  const handleEditConversation = (conversation: TestConversation) => {
    setEditingConversation(conversation)
    setShowEditDialog(true)
  }

  const handleSaveConversation = async (formData: ConversationFormData) => {
    try {
      if (editingConversation) {
        // Update existing conversation
        const updatedConversation = await TestConversationsAPI.Update(editingConversation.id, {
          customerName: formData.customerName,
          customerPhone: formData.customerPhone,
          scenario: formData.scenario,
          scenarioDescription: formData.scenarioDescription,
          sampleMessages: formData.sampleMessages,
          title: formData.title
        } as any).request()

        if (currentConversation?.id === editingConversation.id) {
          setCurrentConversation(updatedConversation)
        }
      } else {
        // Create new conversation
        const newConversation = await createConversation({
          customerName: formData.customerName,
          customerPhone: formData.customerPhone,
          scenario: formData.scenario,
          scenarioDescription: formData.scenarioDescription,
          sampleMessages: formData.sampleMessages,
          title: formData.title
        })
        setCurrentConversation(newConversation)
      }
      setShowEditDialog(false)
      setEditingConversation(null)
    } catch (error) {
      console.error('Failed to save conversation:', error)
    }
  }

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId)
      if (currentConversation?.id === conversationId) {
        setCurrentConversation(conversations.find(c => c.id !== conversationId) || null)
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error)
    }
  }

  const handleClearConversation = async (conversationId: string) => {
    try {
      await clearConversation(conversationId)
    } catch (error) {
      console.error('Failed to clear conversation:', error)
    }
  }

  const handleSendMessage = async (message: string) => {
    if (!currentConversation) return

    try {
      const customerMessage = await sendMessage(currentConversation.id, message)
      await TestConversationsAPI.AddMessage(currentConversation.id, customerMessage).request()
      setCurrentConversation(prev => {
        if (!prev) return prev
        return {
          ...prev,
          messages: [...prev.messages, customerMessage]
        }
      })
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Bot className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-lg font-semibold">{t('title')}</DialogTitle>
                <p className="text-sm text-muted-foreground">
                  {t('subtitle')}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button> */}
            </div>
          </div>
        </DialogHeader>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-80 border-r bg-gray-50 flex flex-col">
            <div className="p-4 border-b">
              <Button
                onClick={handleCreateConversation}
                className="w-full"
                disabled={loading}
              >
                <Plus className="h-4 w-4 mr-2" />
                {t('newConversation')}
              </Button>
            </div>

            <div className="flex-1 overflow-y-auto">
              {loading && (
                <div className="p-8 text-center text-muted-foreground">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                  <p className="text-sm">{t('loading')}</p>
                </div>
              )}
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={`p-4 border-b cursor-pointer hover:bg-white transition-colors ${currentConversation?.id === conversation.id ? 'bg-white border-l-4 border-l-blue-500' : ''
                    }`}
                  onClick={() => setCurrentConversation(conversation)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm truncate">
                          {conversation.customerName}
                        </h4>
                      </div>
                      <p className="text-xs text-muted-foreground truncate">
                        {conversation.customerPhone}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {conversation.messages.length} messages
                      </p>
                    </div>
                    <div className="flex flex-col gap-1 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteConversation(conversation.id)
                        }}
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                        title={t('delete')}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEditConversation(conversation)
                        }}
                        className="h-6 w-6 p-0 text-blue-500 hover:text-blue-700"
                        title={t('edit')}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleClearConversation(conversation.id)
                        }}
                        className="h-6 w-6 p-0"
                        title={t('clear')}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              {conversations.length === 0 && !loading && (
                <div className="p-8 text-center text-muted-foreground">
                  <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-sm">{t('noConversations')}</p>
                  <p className="text-xs mt-1">{t('createFirstConversation')}</p>
                </div>
              )}
            </div>
          </div>

          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col">
            {currentConversation ? (
              <TestChatInterface
                conversation={currentConversation}
                onSendMessage={handleSendMessage}
                loading={loading}
              />
            ) : (
              <div className="flex-1 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <Bot className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">{t('title')}</h3>
                  <p className="text-sm">{t('createFirstConversation')}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {error && (
          <div className="px-6 py-3 bg-red-50 border-t border-red-200 text-red-700 text-sm">
            Error: {error}
          </div>
        )}
      </DialogContent>

      <ConversationEditDialog
        isOpen={showEditDialog}
        onClose={() => {
          setShowEditDialog(false)
          setEditingConversation(null)
        }}
        conversation={editingConversation}
        onSave={handleSaveConversation}
        loading={loading}
      />
    </Dialog>
  )
}
