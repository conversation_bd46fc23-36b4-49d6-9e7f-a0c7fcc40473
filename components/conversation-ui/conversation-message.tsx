import { ConversationMessage } from "@/lib/repositories/conversationMessages"
import { useLocalization } from "@/localization/functions/client"
import clsx from "clsx"
import { locales } from "./locales"

interface ConversationMessageUIProps {
  message: ConversationMessage
  currentUser: string
  participants: Set<{
    id: string
    name: string
  }>
}

export function ConversationMessageUI({
  message,
  currentUser,
  participants,
}: ConversationMessageUIProps) {
  const isFromCurrentUser = message.senderId === currentUser
  const isLogMessage = message.category === "LOG"
  const { t } = useLocalization("conversation-message", locales)

  const getParticipantName = (id: string | undefined | null) => {
    if (!id) return t("unknown_sender")
    const participant = Array.from(participants).find((p) => p.id === id)
    return participant?.name || id
  }

  const getInitials = (name: string) => {
    return name?.charAt(0)?.toUpperCase() || "?"
  }

  const senderName = getParticipantName(message.senderId)
  const initials = getInitials(senderName)

  // Render log messages differently
  if (isLogMessage) {
    return (
      <div className="flex justify-center mb-4">
        <div className="bg-muted/50 text-muted-foreground text-xs px-3 py-2 rounded-full border">
          <span>{message.content}</span>
          <span className="ml-2">
            {new Date(message.createdAt).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </span>
        </div>
      </div>
    )
  }

  return (
    <div
      className={clsx(
        "flex gap-3 mb-4",
        isFromCurrentUser ? "justify-end" : "justify-start",
      )}
    >
      {!isFromCurrentUser && (
        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
          <span className="text-sm font-medium">{initials}</span>
        </div>
      )}

      <div className={clsx("max-w-[60%]")}>
        <div
          className={clsx(
            "rounded-lg p-3 break-words",
            isFromCurrentUser
              ? "bg-primary text-primary-foreground"
              : "bg-muted text-foreground",
          )}
        >
          <p className="text-sm">{message.content}</p>
        </div>
        <div
          className={clsx(
            "flex items-center gap-2 mt-1 text-xs text-muted-foreground",
            isFromCurrentUser ? "justify-end" : "justify-start",
          )}
        >
          {!isFromCurrentUser && <span>{senderName}</span>}
          <span>
            {new Date(message.createdAt).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </span>
        </div>
      </div>

      {isFromCurrentUser && (
        <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center">
          <span className="text-sm font-medium">{initials}</span>
        </div>
      )}
    </div>
  )
}
