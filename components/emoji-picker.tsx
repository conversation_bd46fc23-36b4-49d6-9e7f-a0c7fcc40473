"use client"

import { Button } from "@/components/ui/button"

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void
  className?: string
}

const EMOJI_CATEGORIES = {
  smileys: {
    name: "Smileys & Emotion",
    emojis: [
      "😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂", "🙂", "🙃",
      "😉", "😊", "😇", "🥰", "😍", "🤩", "😘", "😗", "😚", "😙",
      "😋", "😛", "😜", "🤪", "😝", "🤑", "🤗", "🤭", "🤫", "🤔",
      "🤐", "🤨", "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "🤥",
      "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕", "🤢", "🤮", "🤧",
      "🥵", "🥶", "🥴", "😵", "🤯", "🤠", "🥳", "😎", "🤓", "🧐"
    ]
  },
  gestures: {
    name: "People & Body",
    emojis: [
      "👍", "👎", "👌", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉",
      "👆", "🖕", "👇", "☝️", "👋", "🤚", "🖐️", "✋", "🖖", "👏",
      "🙌", "🤲", "🤝", "🙏", "✍️", "💪", "🦾", "🦿", "🦵", "🦶"
    ]
  },
  hearts: {
    name: "Hearts",
    emojis: [
      "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔",
      "❣️", "💕", "💞", "💓", "💗", "💖", "💘", "💝", "💟"
    ]
  },
  objects: {
    name: "Objects",
    emojis: [
      "📱", "💻", "⌨️", "🖥️", "🖨️", "🖱️", "🖲️", "💽", "💾", "💿",
      "📀", "📼", "📷", "📸", "📹", "🎥", "📽️", "🎞️", "📞", "☎️",
      "📟", "📠", "📺", "📻", "🎙️", "🎚️", "🎛️", "⏱️", "⏲️", "⏰"
    ]
  }
}

export function EmojiPicker({ onEmojiSelect, className = "" }: EmojiPickerProps) {
  return (
    <div className={`w-80 h-64 bg-background border rounded-lg shadow-lg ${className}`}>
      <div className="p-3 border-b">
        <h3 className="text-sm font-medium">Choose an emoji</h3>
      </div>

      <div className="p-2 overflow-y-auto h-52">
        {Object.entries(EMOJI_CATEGORIES).map(([key, category]) => (
          <div key={key} className="mb-4">
            <h4 className="text-xs font-medium text-muted-foreground mb-2 px-1">
              {category.name}
            </h4>
            <div className="grid grid-cols-8 gap-1">
              {category.emojis.map((emoji, index) => (
                <Button
                  key={`${key}-${index}`}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-lg hover:bg-accent"
                  onClick={() => onEmojiSelect(emoji)}
                >
                  {emoji}
                </Button>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default EmojiPicker
