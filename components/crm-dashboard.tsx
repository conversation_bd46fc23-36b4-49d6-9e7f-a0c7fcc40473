"use client"

import { useEffect, useState } from "react"
import { ConversationList } from "@/components/conversation-list"
import { ConversationInterface } from "@/components/conversation-interface"
import { CustomerProfile } from "@/components/customer-profile"
import { AgentsSidebar } from "@/components/agents-sidebar"
import { TemplatesPanel } from "@/components/templates-panel"
import { twMerge } from "tailwind-merge"
import { useSendPresence } from "@/hooks/useSendPresence"
import { Conversation } from "@/lib/repositories/conversations"
import { SessionContext } from "@/lib/repositories/auth/types"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "@/components/crm-dashboard/locales"
import { MessageCircle, Users, Zap, ArrowRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export function CRMDashboard(props: { context: SessionContext }) {
  const { t } = useLocalization("crm-dashboard", locales)
  const { sendPresence } = useSendPresence()
  const [messageInput, setMessageInput] = useState("")

  const [selectedConversation, setSelectedConversation] =
    useState<Conversation | null>(null)
  const [showTemplates, setShowTemplates] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [isProfileCollapsed, setIsProfileCollapsed] = useState(false)

  // Handle conversation updates (for both list and interface)
  const handleConversationUpdated = (updatedConversation: Conversation) => {
    setSelectedConversation(updatedConversation)
  }

  // Handle navigation to different conversation
  const handleNavigateToConversation = (conversationId: string) => {
    // For now, we'll just log the navigation
    // In a real app, you might want to fetch the conversation and set it as selected
    console.log(`Navigating to conversation: ${conversationId}`)
    // TODO: Implement actual navigation logic
    // This could involve fetching the conversation by ID and setting it as selected
  }

  // Presence handling (disabled for now, uncomment if needed)
  useEffect(() => {
    // sendPresence({ presence: "online" })
    // const handleBeforeUnload = () => {
    //   navigator.sendBeacon("/api/v1/functions/conversations/send-presence", JSON.stringify({
    //     presence: "offline",
    //   }))
    // }
    // window.addEventListener("beforeunload", handleBeforeUnload)
    // return () => window.removeEventListener("beforeunload", handleBeforeUnload)
  }, [])

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      {/* Sidebar: Conversation list */}
      <div className="flex-shrink-0">
        <ConversationList
          selectedConversation={selectedConversation}
          onSelectConversation={setSelectedConversation}
          selectedTags={selectedTags}
          onTagsChange={setSelectedTags}
          onConversationUpdated={handleConversationUpdated}
        />
      </div>

      {/* Main conversation interface */}
      <div className="flex-1 min-w-0 flex flex-col border-l border-r">
        {selectedConversation ? (
          <ConversationInterface
            key={selectedConversation.id}
            conversation={selectedConversation}
            messageInput={messageInput}
            setMessageInput={setMessageInput}
            onToggleTemplates={() => setShowTemplates((prev) => !prev)}
            showTemplates={showTemplates}
            isProfileCollapsed={isProfileCollapsed}
            currentUserId={props.context.user.id}
            onConversationUpdated={handleConversationUpdated}
            onNavigateToConversation={handleNavigateToConversation}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center p-8">
            <Card className="max-w-md w-full border-dashed">
              <CardContent className="pt-6">
                <div className="text-center space-y-6">
                  {/* Icon */}
                  <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                    <MessageCircle className="h-8 w-8 text-muted-foreground" />
                  </div>

                  {/* Main Message */}
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold">{t("empty_state.title")}</h3>
                    <p className="text-sm text-muted-foreground">
                      {t("empty_state.description")}
                    </p>
                  </div>

                  {/* Features */}
                  <div className="space-y-3 text-left">
                    <div className="flex items-center gap-3 text-sm">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <MessageCircle className="h-4 w-4 text-blue-600" />
                      </div>
                      <span>{t("empty_state.features.messaging")}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <Zap className="h-4 w-4 text-green-600" />
                      </div>
                      <span>{t("empty_state.features.ai_suggestions")}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <Users className="h-4 w-4 text-purple-600" />
                      </div>
                      <span>{t("empty_state.features.collaboration")}</span>
                    </div>
                  </div>

                  {/* Call to Action */}
                  <div className="pt-4">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        const searchInput = document.querySelector('input[id*="conversation-search"]') as HTMLInputElement
                        if (searchInput) {
                          searchInput.focus()
                        }
                      }}
                    >
                      <ArrowRight className="h-4 w-4 mr-2" />
                      {t("empty_state.action")}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Right sidebar: Customer profile and agents */}
      {/* <div
        className={twMerge(
          "flex-shrink-0 bg-muted/30 flex flex-col overflow-hidden transition-all duration-300 ease-in-out",
          isProfileCollapsed ? "w-[56px]" : "w-80",
        )}
      >
        <CustomerProfile
          conversationId={selectedConversation?.id || ""}
          customerProfileId={selectedConversation?.customerProfileId}
          isCollapsed={isProfileCollapsed}
          onToggle={() => setIsProfileCollapsed((prev) => !prev)}
        />
        <AgentsSidebar conversationId={selectedConversation?.id || ""} />
      </div> */}

      {/* Templates panel */}
      {showTemplates && (
        <div className="right-80 top-0 bottom-0 z-10 shadow-lg">
          <TemplatesPanel onClose={() => setShowTemplates(false)}
            onUseTemplate={(template) => {
              setMessageInput(template)
            }}
          />
        </div>
      )}
    </div>
  )
}
