"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  ChevronDown,
  ChevronUp,
  History,
  MessageCircle,
  Calendar,
  User,
  ExternalLink
} from "lucide-react"
import { ConversationsAPI } from "@/lib/services/conversationApi"
import { Conversation } from "@/lib/repositories/conversations/interface"
import { formatDistanceToNow } from "date-fns"

interface PreviousConversationCardProps {
  conversation: Conversation
  onNavigateToPrevious?: (previousConversationId: string) => void
  className?: string
}

export function PreviousConversationCard({
  conversation,
  onNavigateToPrevious,
  className = ""
}: PreviousConversationCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [previousConversation, setPreviousConversation] = useState<Conversation | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Check if there's a previous conversation
  useEffect(() => {
    const fetchPreviousConversation = async () => {
      // If no previousConversationId, don't fetch
      if (!conversation.previousConversationId) {
        setPreviousConversation(null)
        return
      }

      setLoading(true)
      setError(null)

      try {
        const response = await ConversationsAPI.Detail(conversation.previousConversationId).request()
        setPreviousConversation(response)
      } catch (err: any) {
        setError(err.message || "Failed to load previous conversation")
        setPreviousConversation(null)
      } finally {
        setLoading(false)
      }
    }

    fetchPreviousConversation()
  }, [conversation.previousConversationId])

  // Don't render if no previous conversation ID exists
  if (!conversation.previousConversationId) {
    return null
  }

  // Don't render if loading failed and no previous conversation
  if (!loading && !previousConversation && !error) {
    return null
  }

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  const handleNavigate = () => {
    if (previousConversation && onNavigateToPrevious) {
      onNavigateToPrevious(previousConversation.id)
    }
  }

  const formatDate = (date: Date | string) => {
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date
      return formatDistanceToNow(dateObj, { addSuffix: true })
    } catch {
      return 'Unknown time'
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'CLOSED':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  if (loading) {
    return (
      <Card className={`mx-4 mb-2 ${className}`}>
        <CardContent className="p-3">
          <div className="flex items-center gap-2">
            <History className="h-4 w-4 text-muted-foreground" />
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-6 w-16 ml-auto" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={`mx-4 mb-2 border-red-200 ${className}`}>
        <CardContent className="p-3">
          <div className="flex items-center gap-2 text-red-600">
            <History className="h-4 w-4" />
            <span className="text-sm">Error loading previous conversation</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`mx-4 mb-2 border-blue-200 bg-blue-50/50 ${className}`}>
      <CardContent className="p-3">
        {/* Collapsed view */}
        <div className="flex items-center gap-2">
          <History className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-900">
            Previous conversation available
          </span>
          <Badge
            variant="outline"
            className={`ml-auto text-xs ${getStatusColor(previousConversation?.status)}`}
          >
            {previousConversation?.status || 'Unknown'}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleExpanded}
            className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Expanded view */}
        {isExpanded && previousConversation && (
          <div className="mt-3 pt-3 border-t border-blue-200">
            <div className="space-y-2">
              {/* Conversation name */}
              <div className="flex items-center gap-2">
                <User className="h-3 w-3 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {previousConversation.name || 'Unnamed Conversation'}
                </span>
              </div>

              {/* Description */}
              {previousConversation.description && (
                <div className="flex items-start gap-2">
                  <MessageCircle className="h-3 w-3 text-muted-foreground mt-0.5" />
                  <span className="text-xs text-muted-foreground">
                    {previousConversation.description}
                  </span>
                </div>
              )}

              {/* Last message */}
              {previousConversation.lastMessage && (
                <div className="flex items-start gap-2">
                  <MessageCircle className="h-3 w-3 text-muted-foreground mt-0.5" />
                  <div className="flex-1 min-w-0">
                    <span className="text-xs text-muted-foreground block truncate">
                      {previousConversation.lastMessage.body}
                    </span>
                  </div>
                </div>
              )}

              {/* Timestamps */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>Created {formatDate(previousConversation.createdAt)}</span>
                </div>
                {previousConversation.lastMessageAt && (
                  <div className="flex items-center gap-1">
                    <MessageCircle className="h-3 w-3" />
                    <span>Last message {formatDate(previousConversation.lastMessageAt)}</span>
                  </div>
                )}
              </div>

              {/* Tags */}
              {previousConversation.tags && previousConversation.tags.length > 0 && (
                <div className="flex items-center gap-1 flex-wrap">
                  {previousConversation.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {previousConversation.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{previousConversation.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              )}

              {/* Navigate button */}
              <div className="pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNavigate}
                  className="w-full text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <ExternalLink className="h-3 w-3 mr-2" />
                  View Previous Conversation
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default PreviousConversationCard
