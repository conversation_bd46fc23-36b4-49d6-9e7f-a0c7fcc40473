"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Edit2, X, CheckCircle, ChevronDown, ChevronUp, Bot, BotOff } from "lucide-react"
import { Conversation } from "@/lib/repositories/conversations"
import { useConversationAI } from "@/hooks/useConversationAI"


interface ConversationHeaderProps {
  customerName: string
  customerEmail: string
  orderNumber: string
  status: string
  tags: string[]
  description?: string
  conversation?: Conversation
  onEditConversation: () => Promise<void>
  onCloseConversation: () => Promise<void>
  onConversationUpdated?: (updatedConversation: Conversation) => void
}

export function ConversationHeader({
  customerName,
  customerEmail,
  orderNumber,
  status,
  tags,
  description,
  conversation,
  onEditConversation,
  onCloseConversation,
  onConversationUpdated,
}: ConversationHeaderProps) {
  const [isCompact, setIsCompact] = useState(false)
  const [isClosing, setIsClosing] = useState(false)

  const { isToggling, toggleAI } = useConversationAI({
    conversation: conversation!,
    onConversationUpdated,
  })

  const toggleCompact = () => setIsCompact((prev) => !prev)

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
      case "typing":
      case "open":
        return "bg-green-50 text-green-700 border-green-200"
      case "pending":
        return "bg-yellow-50 text-yellow-700 border-yellow-200"
      case "closed":
        return "bg-red-50 text-red-700 border-red-200"
      case "resolved":
        return "bg-blue-50 text-blue-700 border-blue-200"
      default:
        return "bg-gray-50 text-gray-700 border-gray-200"
    }
  }

  const isClosed = conversation?.status === "CLOSED"

  const closeConversation = async () => {
    if (onCloseConversation === undefined) return
    setIsClosing(true)
    try {
      await onCloseConversation()
    } catch (error) {
      console.error("Failed to close conversation:", error)
    } finally {
      setIsClosing(false)
    }
  }

  return (
    <div className="border-b p-4 bg-background">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-semibold">{customerName}</h2>
            {onEditConversation && !isCompact && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onEditConversation}
                className="h-6 w-6 p-0"
                title="Edit conversation"
              >
                <Edit2 className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* Compact mode skips details */}
          {!isCompact && (
            <>
              <p className="text-sm text-muted-foreground">
                {customerEmail} • {orderNumber}
              </p>
              {description && (
                <p className="text-sm text-muted-foreground mt-1 italic">
                  {description}
                </p>
              )}
              <div className="flex gap-2 mt-1 flex-wrap">
                <Badge
                  variant="default"
                  className={getStatusColor(conversation?.status || status)}
                >
                  {conversation?.status || status}
                </Badge>
                {tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
            </>
          )}

          {/* Compact mode shows only status badge */}
          {isCompact && (
            <div className="mt-1">
              <Badge
                variant="default"
                className={getStatusColor(conversation?.status || status)}
              >
                {conversation?.status || status}
              </Badge>
            </div>
          )}
        </div>

        {/* Right side: AI Toggle, Compact Toggle & Close button */}
        <div className="flex items-center gap-2">
          {/* AI Toggle */}
          {conversation && !conversation.isTesting && !isClosed && (
            <div className="flex items-center gap-2 px-2 py-1 rounded-md border bg-background">
              <div className="flex items-center gap-1">
                {conversation.isAiEnabled !== false ? (
                  <Bot className="h-4 w-4 text-green-600" />
                ) : (
                  <BotOff className="h-4 w-4 text-red-600" />
                )}
                <span className="text-xs font-medium">
                  AI {conversation.isAiEnabled !== false ? "ON" : "OFF"}
                </span>
              </div>
              <Switch
                checked={conversation.isAiEnabled !== false}
                onCheckedChange={toggleAI}
                disabled={isToggling}
              />
            </div>
          )}

          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={toggleCompact}
            title={isCompact ? "Expand details" : "Compact view"}
          >
            {isCompact ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
          </Button>

          {(
            <Button
              variant={isClosed ? "outline" : "destructive"}
              size="sm"
              onClick={closeConversation}
              disabled={isClosed || isClosing}
              className="flex items-center gap-2"
              title={isClosed ? "Conversation is closed" : "Close conversation"}
            >
              {isClosing ? (
                <div className="flex space-x-1">
                  <span className="w-2 h-2 bg-white rounded-full animate-bounce [animation-delay:-0.3s]" />
                  <span className="w-2 h-2 bg-white rounded-full animate-bounce [animation-delay:-0.15s]" />
                  <span className="w-2 h-2 bg-white rounded-full animate-bounce" />
                </div>
              ) : isClosed ? (
                <>
                  <CheckCircle className="h-4 w-4" />
                  Closed
                </>
              ) : (
                <>
                  <X className="h-4 w-4" />
                  Close
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
