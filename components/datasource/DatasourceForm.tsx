"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Save, FileText, Globe, Database, Eye, EyeOff } from "lucide-react"
import { Datasource } from "@/lib/repositories/datasources/interface"

interface DatasourceFormProps {
  initialDatasource?: Datasource | null
  onSave: (data: {
    name: string
    type: string
    content?: string
    url?: string
    accessKey?: string
    isActive: boolean
  }) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
  submitButtonText?: string
  title: string
  description: string
}

export default function DatasourceForm({
  initialDatasource,
  onSave,
  onCancel,
  isSubmitting = false,
  submitButtonText = "Save",
  title,
  description,
}: DatasourceFormProps) {
  const [showAccessKey, setShowAccessKey] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    type: "TEXT",
    content: "",
    url: "",
    accessKey: "",
    isActive: true,
  })

  // Initialize form with initial datasource data
  useEffect(() => {
    if (initialDatasource) {
      setFormData({
        name: initialDatasource.name || "",
        type: initialDatasource.type || "TEXT",
        content: initialDatasource.content || "",
        url: initialDatasource.url || "",
        accessKey: initialDatasource.accessKey || "",
        isActive: initialDatasource.isActive ?? true,
      })
    }
  }, [initialDatasource])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim()) {
      alert("Please enter a name")
      return
    }

    if (formData.type === "TEXT" && !formData.content.trim()) {
      alert("Please enter content for TEXT type datasource")
      return
    }

    if (formData.type !== "TEXT" && !formData.url.trim()) {
      alert("Please enter a URL for non-TEXT type datasource")
      return
    }

    try {
      await onSave({
        name: formData.name.trim(),
        type: formData.type,
        content: formData.type === "TEXT" ? formData.content.trim() : undefined,
        url: formData.type !== "TEXT" ? formData.url.trim() : undefined,
        accessKey: formData.accessKey.trim() || undefined,
        isActive: formData.isActive,
      })
    } catch (error) {
      console.error("Error saving datasource:", error)
      alert("Failed to save datasource. Please try again.")
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "TEXT":
        return <FileText className="h-5 w-5" />
      case "API":
        return <Globe className="h-5 w-5" />
      default:
        return <Database className="h-5 w-5" />
    }
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{title}</h1>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getTypeIcon(formData.type)}
              Datasource Details
            </CardTitle>
            <CardDescription>Enter the basic information for your datasource</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter datasource name"
                maxLength={100}
                required
              />
              <p className="text-sm text-gray-500">{formData.name.length}/100 characters</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="TEXT">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Text Content
                    </div>
                  </SelectItem>
                  {/* <SelectItem value="API">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      API Endpoint
                    </div>
                  </SelectItem>
                  <SelectItem value="DATABASE">
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Database
                    </div>
                  </SelectItem> */}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Content/URL Section */}
        <Card>
          <CardHeader>
            <CardTitle>
              {formData.type === "TEXT" ? "Content" : "Connection Details"}
            </CardTitle>
            <CardDescription>
              {formData.type === "TEXT"
                ? "Enter the text content for this datasource"
                : "Enter the connection details for this datasource"
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.type === "TEXT" ? (
              <div className="space-y-2">
                <Label htmlFor="content">Content</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Enter your text content here..."
                  className="min-h-40"
                  maxLength={10000}
                  required
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>{formData.content.length}/10,000 characters</span>
                  <span className={formData.content.length > 9000 ? "text-orange-500" : ""}>
                    {formData.content.length > 9000 && "Approaching limit"}
                  </span>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="url">URL</Label>
                <Input
                  id="url"
                  type="url"
                  value={formData.url}
                  onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="https://example.com/api"
                  required
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="accessKey">Access Key (Optional)</Label>
              <div className="relative">
                <Input
                  id="accessKey"
                  type={showAccessKey ? "text" : "password"}
                  value={formData.accessKey}
                  onChange={(e) => setFormData(prev => ({ ...prev, accessKey: e.target.value }))}
                  placeholder="Enter access key if required"
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowAccessKey(!showAccessKey)}
                >
                  {showAccessKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !formData.name.trim() ||
              (formData.type === "TEXT" && !formData.content.trim()) ||
              (formData.type !== "TEXT" && !formData.url.trim())
            }
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                {submitButtonText}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
