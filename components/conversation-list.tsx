"use client"

import { useState, useEffect, useCallback } from "react"
import { PanelRightClose, PanelLeftClose } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { OfflineIndicator } from "@/components/SyncStatusIndicator"
import { EditConversationDialog } from "@/components/edit-conversation-dialog"
import { twMerge } from "tailwind-merge"
import { ConversationsAPI } from "@/lib/services/conversationApi"
import { useConversationListState } from "@/hooks/useConversationListState"
import { useConversationListRealtime } from "@/hooks/useConversationListRealtime"
import { ConversationListHeader } from "@/components/conversation-list/ConversationListHeader"
import { ConversationListContent } from "@/components/conversation-list/ConversationListContent"
import { Conversation } from "@/lib/repositories/conversations"
import { toast } from "sonner"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "@/components/crm-dashboard/locales"

const K_PERPAGE = 20

interface ConversationListProps {
  selectedConversation: Conversation | null
  onSelectConversation: (props: Conversation) => void
  selectedTags: string[]
  onTagsChange: (tags: string[]) => void
  onConversationUpdated?: (updatedConversation: Conversation) => void
}

export function ConversationList({
  selectedConversation,
  onSelectConversation,
  selectedTags,
  onTagsChange,
  onConversationUpdated,
}: ConversationListProps) {
  const { t } = useLocalization("crm-dashboard", locales)
  const [page, setPage] = useState(1)
  const [data, setData] = useState<Conversation[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [isLastConversations, setIsLastConversations] = useState(false)
  const [editingConversation, setEditingConversation] = useState<Conversation | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)

  // Conversation list state management
  const {
    searchQuery,
    setSearchQuery,
    showOfflineData,
    lastSyncInfo,
    isCompact,
    containerRef,
    listRef,
    toggleCompactMode,
    toggleOfflineData,
  } = useConversationListState()

  // Fetch conversations from API
  const fetchConversations = useCallback(async (pageNum: number = 1, search: string = "", isNewSearch: boolean = false) => {
    try {
      setLoading(true)
      setError(null)

      const response = await ConversationsAPI.All({
        page: pageNum,
        per_page: K_PERPAGE,
        search: search || undefined,
        sort: [
          { field: "lastMessageAt", direction: "DESC" },
          { field: "updatedAt", direction: "DESC" },
        ],
        filters: selectedTags.length > 0 ? [{ field: "tags", value: selectedTags }] : [],
      }).request()

      if (response && response.items) {
        if (isNewSearch || pageNum === 1) {
          // Reset data for new search or first page
          setData(response.items)
        } else {
          // Append data for pagination
          setData(prev => prev ? [...prev, ...response.items] : response.items)
        }

        setIsLastConversations(response.items.length < K_PERPAGE)
      }
    } catch (err: any) {
      const message = err.response?.data?.error || err.message || "Failed to fetch conversations"
      setError(message)
      toast.error(message)
    } finally {
      setLoading(false)
    }
  }, [selectedTags])

  // Initial load and search changes
  useEffect(() => {
    setPage(1)
    fetchConversations(1, searchQuery, true)
  }, [searchQuery, selectedTags, fetchConversations])

  // Page changes (pagination)
  useEffect(() => {
    if (page > 1) {
      fetchConversations(page, searchQuery, false)
    }
  }, [page, fetchConversations, searchQuery])

  // Refetch function for manual refresh
  const refetch = useCallback(() => {
    setPage(1)
    fetchConversations(1, searchQuery, true)
  }, [fetchConversations, searchQuery])

  // Handle conversation updates from external sources (like message sending)
  const handleConversationUpdate = useCallback((updatedConversation: Conversation) => {
    if (!data) return

    // Update the conversation in the list and move it to the top
    const otherConversations = data.filter(conv => conv.id !== updatedConversation.id)
    const existingConversation = data.find(conv => conv.id === updatedConversation.id)

    if (existingConversation) {
      // Merge the updates with existing conversation
      const mergedConversation = { ...existingConversation, ...updatedConversation }
      setData([mergedConversation, ...otherConversations])
    }

    // Also call the parent callback if provided
    if (onConversationUpdated) {
      onConversationUpdated(updatedConversation)
    }
  }, [data, onConversationUpdated])

  // Handle edit conversation
  const handleEditConversation = useCallback((conversation: Conversation) => {
    setEditingConversation(conversation)
    setShowEditDialog(true)
  }, [])

  // Handle conversation update from edit dialog
  const handleConversationUpdatedFromDialog = useCallback((updatedConversation: Conversation) => {
    handleConversationUpdate(updatedConversation)
    setShowEditDialog(false)
    setEditingConversation(null)
  }, [handleConversationUpdate])

  // Real-time updates
  useConversationListRealtime({
    data,
    setData,
    selectedConversation,
    onSelectConversation,
    refetch,
    onConversationUpdate: handleConversationUpdate,
  })


  const filteredConversations = data || []
  return (
    <div
      ref={containerRef}
      className={twMerge(
        "h-full flex-shrink-0 border-r flex flex-col",
        isCompact ? "w-20" : "w-80",
      )}
    >
      <Button
        size="icon"
        variant="ghost"
        onClick={toggleCompactMode}
        title={t("conversation_list.toggle_compact_mode")}
      >
        {isCompact ? (
          <PanelRightClose className="h-4 w-4" />
        ) : (
          <PanelLeftClose className="h-4 w-4" />
        )}
      </Button>

      {!isCompact && (
        <>
          <OfflineIndicator />
          <ConversationListHeader
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            selectedTags={selectedTags}
            onTagsChange={onTagsChange}
            showOfflineData={showOfflineData}
            toggleOfflineData={toggleOfflineData}
            lastSyncInfo={lastSyncInfo}
            onRefresh={refetch}
            loading={loading}
          />
        </>
      )}

      <ConversationListContent
        conversations={filteredConversations}
        selectedConversation={selectedConversation}
        onSelectConversation={onSelectConversation}
        onEditConversation={handleEditConversation}
        isCompact={isCompact}
        loading={loading}
        error={error}
        listRef={listRef}
        page={page}
        setPage={setPage}
        isLastConversations={isLastConversations}
        searchQuery={searchQuery}
      />

      {editingConversation && (
        <EditConversationDialog
          conversation={editingConversation}
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          onConversationUpdated={handleConversationUpdatedFromDialog}
        />
      )}
    </div>
  )
}
