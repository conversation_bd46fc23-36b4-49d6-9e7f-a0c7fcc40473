"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  Calendar,
  Users,
  MessageSquare,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { Broadcast, BroadcastStatus } from "@/lib/repositories/broadcast/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface BroadcastGridItemProps {
  broadcast: Broadcast
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
  onStart?: (id: string) => void
  onCancel?: (id: string) => void
  onView?: (id: string) => void
  className?: string
}

const statusConfig = {
  [BroadcastStatus.DRAFT]: {
    color: "bg-gray-100 text-gray-800 border-gray-200",
    icon: Edit,
  },
  [BroadcastStatus.SCHEDULED]: {
    color: "bg-blue-100 text-blue-800 border-blue-200",
    icon: Calendar,
  },
  [BroadcastStatus.SENDING]: {
    color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    icon: Clock,
  },
  [BroadcastStatus.COMPLETED]: {
    color: "bg-green-100 text-green-800 border-green-200",
    icon: CheckCircle,
  },
  [BroadcastStatus.FAILED]: {
    color: "bg-red-100 text-red-800 border-red-200",
    icon: XCircle,
  },
  [BroadcastStatus.CANCELLED]: {
    color: "bg-gray-100 text-gray-800 border-gray-200",
    icon: AlertCircle,
  },
}

export function BroadcastGridItem({
  broadcast,
  onEdit,
  onDelete,
  onStart,
  onCancel,
  onView,
  className
}: BroadcastGridItemProps) {
  const router = useRouter()
  const { t } = useLocalization("broadcast", locales)
  const config = statusConfig[broadcast.status]
  const StatusIcon = config.icon

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getSuccessRate = () => {
    if (broadcast.totalTargets === 0) return 0
    return Math.round((broadcast.sentCount / broadcast.totalTargets) * 100)
  }

  const getProgressPercentage = () => {
    if (broadcast.totalTargets === 0) return 0
    return Math.round(((broadcast.sentCount + broadcast.failedCount) / broadcast.totalTargets) * 100)
  }

  const handleDefaultView = () => {
    if (onView) {
      onView(broadcast.id)
    } else {
      router.push(`/broadcast/${broadcast.id}`)
    }
  }

  const handleDefaultEdit = () => {
    if (onEdit) {
      onEdit(broadcast.id)
    } else {
      router.push(`/broadcast/${broadcast.id}/edit`)
    }
  }

  return (
    <Card className={cn("hover:shadow-lg transition-all duration-200 cursor-pointer group", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg leading-tight truncate group-hover:text-primary transition-colors">
              {broadcast.title}
            </h3>
            <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
              {broadcast.message}
            </p>
          </div>
          <div className="flex items-center gap-2 ml-2">
            <Badge variant="outline" className={cn("border text-xs", config.color)}>
              <StatusIcon className="h-3 w-3 mr-1" />
              {t(`item.status.${broadcast.status}`)}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleDefaultView}>
                  <Eye className="mr-2 h-4 w-4" />
                  {t("item.actions.view_details")}
                </DropdownMenuItem>
                {broadcast.status === BroadcastStatus.DRAFT && (
                  <>
                    <DropdownMenuItem onClick={handleDefaultEdit}>
                      <Edit className="mr-2 h-4 w-4" />
                      {t("item.actions.edit")}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onStart?.(broadcast.id)}>
                      <Play className="mr-2 h-4 w-4" />
                      {t("item.actions.start_broadcast")}
                    </DropdownMenuItem>
                  </>
                )}
                {broadcast.status === BroadcastStatus.SENDING && (
                  <DropdownMenuItem onClick={() => onCancel?.(broadcast.id)}>
                    <Pause className="mr-2 h-4 w-4" />
                    {t("item.actions.cancel_broadcast")}
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete?.(broadcast.id)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t("item.actions.delete")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0" onClick={handleDefaultView}>
        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                  <Users className="h-4 w-4 text-blue-600" />
                  <div>
                    <div className="text-sm font-medium">{broadcast.totalTargets}</div>
                    <div className="text-xs text-muted-foreground">{t("item.stats.targets")}</div>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t("item.tooltips.targets")}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <div>
                    <div className="text-sm font-medium">{broadcast.sentCount}</div>
                    <div className="text-xs text-muted-foreground">{t("item.stats.sent")}</div>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t("item.tooltips.sent")}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                  <XCircle className="h-4 w-4 text-red-600" />
                  <div>
                    <div className="text-sm font-medium">{broadcast.failedCount}</div>
                    <div className="text-xs text-muted-foreground">{t("item.stats.failed")}</div>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t("item.tooltips.failed")}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-purple-600" />
                  <div>
                    <div className="text-sm font-medium">{getSuccessRate()}%</div>
                    <div className="text-xs text-muted-foreground">{t("item.stats.success")}</div>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t("item.tooltips.success")}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Progress Bar */}
        {broadcast.status === BroadcastStatus.SENDING || broadcast.status === BroadcastStatus.COMPLETED ? (
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span>{t("item.progress.title")}</span>
              <span>{getProgressPercentage()}%</span>
            </div>
            <Progress value={getProgressPercentage()} className="h-2" />
            <div className="text-xs text-muted-foreground text-center">
              {t("item.progress.processed", {
                processed: broadcast.sentCount + broadcast.failedCount,
                total: broadcast.totalTargets
              })}
            </div>
          </div>
        ) : null}

        {/* Footer */}
        <div className="pt-3 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {formatDate(broadcast.createdAt)}
            </div>
            {broadcast.scheduledAt && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {formatDate(broadcast.scheduledAt)}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
