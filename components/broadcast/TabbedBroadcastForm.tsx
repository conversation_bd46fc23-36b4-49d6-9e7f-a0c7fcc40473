"use client"

import { useState, useEffect } from "react"
import { ArrowLeft, MessageSquare, Users, Send, Smartphone, Tag, X, Check, Search } from "lucide-react"
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "../ui/card"
import { Input } from "../ui/input"
import { Textarea } from "../ui/textarea"
import { Label } from "../ui/label"
import { Button } from "../ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "../ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { Badge } from "../ui/badge"
import { BroadcastRecipientsAPI, DevicesAPI } from "@/lib/services"
import type { TagWithCount, BroadcastRecipientContact } from "@/lib/services/broadcastRecipientsApi"
import { Broadcast } from "@/lib/repositories/broadcast"
import { Device } from "@/lib/repositories/devices/interface"
import { MobileChatPreview } from "./MobileChatPreview"
import { useLocalization } from "@/localization/functions/client"
import { enhancedBroadcastFormLocales } from "@/localization/enhanced-broadcast-form"

interface TabbedBroadcastFormProps {
  initialBroadcast?: Broadcast | null
  onSave: (data: {
    title: string
    message: string
    deviceId: string
    recipientTags?: string[]
    excludedRecipientIds?: string[]
    recipientSelectionType: "manual" | "tags"
  }) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
  submitButtonText?: string
  title: string
  description: string
}

export default function TabbedBroadcastForm({
  initialBroadcast = null,
  onSave,
  onCancel,
  isSubmitting = false,
  submitButtonText = "Save Broadcast",
  title,
  description,
}: TabbedBroadcastFormProps) {
  const { t } = useLocalization("enhanced-broadcast-form", enhancedBroadcastFormLocales)

  // Form state
  const [broadcastTitle, setBroadcastTitle] = useState(initialBroadcast?.title || "")
  const [message, setMessage] = useState(initialBroadcast?.message || "")
  const [selectedDeviceId, setSelectedDeviceId] = useState(initialBroadcast?.deviceId || "")

  // Recipient selection state
  const [selectedTags, setSelectedTags] = useState<string[]>(initialBroadcast?.recipientTags || [])
  const [excludedContactIds, setExcludedContactIds] = useState<string[]>(initialBroadcast?.excludedRecipientIds || [])
  const [availableTags, setAvailableTags] = useState<TagWithCount[]>([])
  const [contacts, setContacts] = useState<BroadcastRecipientContact[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalRecipients, setTotalRecipients] = useState(0)
  const [loading, setLoading] = useState(false)

  // Device state
  const [devices, setDevices] = useState<Device[]>([])
  const [loadingDevices, setLoadingDevices] = useState(false)

  // Load devices
  useEffect(() => {
    const loadDevices = async () => {
      setLoadingDevices(true)
      try {
        const response = await DevicesAPI.All({ page: 1, per_page: 100 }).request()
        setDevices(response.items || [])
      } catch (error) {
        console.error("Failed to load devices:", error)
      } finally {
        setLoadingDevices(false)
      }
    }
    loadDevices()
  }, [])

  // Load available tags and initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const response = await BroadcastRecipientsAPI.GetAvailableTags().request()
        setAvailableTags(response.availableTags || [])
      } catch (error) {
        console.error("Failed to load available tags:", error)
      }
    }
    loadInitialData()
  }, [])

  // Load contacts when filters change
  useEffect(() => {
    const loadContacts = async () => {
      setLoading(true)
      try {
        const response = await BroadcastRecipientsAPI.All({
          page: currentPage,
          per_page: 20,
          search: searchTerm || undefined,
          // tags: selectedTags.length > 0 ? selectedTags : undefined,
          // excludedIds: excludedContactIds.length > 0 ? excludedContactIds : undefined,
        }).request()

        setContacts(response.contacts || [])
        setTotalRecipients(response.selectedCount || 0)
      } catch (error) {
        console.error("Failed to load contacts:", error)
        setContacts([])
        setTotalRecipients(0)
      } finally {
        setLoading(false)
      }
    }

    loadContacts()
  }, [selectedTags, excludedContactIds, searchTerm, currentPage])

  const handleTagToggle = (tagName: string) => {
    const newTags = selectedTags.includes(tagName)
      ? selectedTags.filter(t => t !== tagName)
      : [...selectedTags, tagName]
    setSelectedTags(newTags)
    setCurrentPage(1)
  }

  const handleContactExclude = (contactId: string) => {
    const newExcluded = excludedContactIds.includes(contactId)
      ? excludedContactIds.filter(id => id !== contactId)
      : [...excludedContactIds, contactId]
    setExcludedContactIds(newExcluded)
  }

  // Calculate final recipient count
  const calculateRecipientCount = () => {
    if (selectedTags.length === 0) {
      // No tags selected = all contacts minus excluded
      return Math.max(0, totalRecipients - excludedContactIds.length)
    } else {
      // Tags selected = sum of tag counts minus excluded contacts that have those tags
      const taggedContactsCount = availableTags
        .filter(tagObj => selectedTags.includes(tagObj.tag))
        .reduce((sum, tagObj) => sum + tagObj.count, 0)

      // Count excluded contacts that have selected tags
      const excludedTaggedContacts = contacts.filter(contact =>
        excludedContactIds.includes(contact.id) &&
        contact.tags.some(tag => selectedTags.includes(tag))
      ).length

      return Math.max(0, taggedContactsCount - excludedTaggedContacts)
    }
  }

  const finalRecipientCount = calculateRecipientCount()

  // Check if contact should be included (green)
  const shouldContactBeIncluded = (contact: BroadcastRecipientContact) => {
    // If contact is excluded, it should not be included
    if (excludedContactIds.includes(contact.id)) return false

    if (selectedTags.length === 0) {
      // No tags selected = all contacts should be included
      return true
    } else {
      // Tags selected = only contacts with selected tags should be included
      return contact.tags.some(tag => selectedTags.includes(tag))
    }
  }

  // Determine button action for contact
  const getContactButtonAction = (contact: BroadcastRecipientContact) => {
    const isExcluded = excludedContactIds.includes(contact.id)

    if (isExcluded) {
      return 'include' // Show include button for excluded contacts
    }

    if (selectedTags.length === 0) {
      // No tags selected = all contacts are green, show exclude button
      return 'exclude'
    } else {
      // Tags selected
      const hasSelectedTags = contact.tags.some(tag => selectedTags.includes(tag))
      if (hasSelectedTags) {
        // Contact has selected tags = green, show exclude button
        return 'exclude'
      } else {
        // Contact doesn't have selected tags = not green, show include button
        return 'include'
      }
    }
  }

  const messageLength = message.length
  const maxLength = 1000

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!broadcastTitle.trim()) {
      alert(t("form.validation.title_required"))
      return
    }

    if (!message.trim()) {
      alert(t("form.validation.message_required"))
      return
    }

    if (!selectedDeviceId) {
      alert(t("form.validation.device_required"))
      return
    }

    const recipientSelectionType: "manual" | "tags" = "tags"

    await onSave({
      title: broadcastTitle.trim(),
      message: message.trim(),
      deviceId: selectedDeviceId,
      recipientTags: selectedTags.length > 0 ? selectedTags : undefined,
      excludedRecipientIds: excludedContactIds.length > 0 ? excludedContactIds : undefined,
      recipientSelectionType,
    })
  }

  return (
    <div className="w-full h-full px-60 overflow-y-auto">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="sm" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("form.buttons.back")}
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
      </div>

      <div className="h-full">
        <Tabs defaultValue="broadcast-info" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="broadcast-info" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              {t("tabs.broadcast_info")}
            </TabsTrigger>
            <TabsTrigger value="recipients" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              {t("tabs.recipients")}
            </TabsTrigger>
          </TabsList>

          {/* Broadcast Info Tab */}
          <TabsContent value="broadcast-info" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Form */}
              <div className="space-y-6">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5" />
                      {t("form.sections.basic_info")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="title">{t("form.fields.title")}</Label>
                      <Input
                        id="title"
                        value={broadcastTitle}
                        onChange={(e) => setBroadcastTitle(e.target.value)}
                        placeholder={t("form.placeholders.title")}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="message">{t("form.fields.message")}</Label>
                      <Textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder={t("form.placeholders.message")}
                        rows={4}
                        maxLength={maxLength}
                        required
                      />
                      <div className="flex justify-between text-sm text-muted-foreground mt-1">
                        <span>{t("form.hints.message_hint")}</span>
                        <span className={messageLength > maxLength * 0.9 ? "text-orange-500" : ""}>
                          {messageLength}/{maxLength}
                        </span>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="device">{t("form.fields.device")}</Label>
                      <Select value={selectedDeviceId} onValueChange={setSelectedDeviceId} required>
                        <SelectTrigger>
                          <SelectValue placeholder={t("form.placeholders.device")} />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingDevices ? (
                            <SelectItem value="loading" disabled>
                              {t("form.loading.devices")}
                            </SelectItem>
                          ) : devices.length === 0 ? (
                            <SelectItem value="empty" disabled>
                              {t("form.empty.devices")}
                            </SelectItem>
                          ) : (
                            devices.map((device) => (
                              <SelectItem key={device.id} value={device.id}>
                                <div className="flex items-center gap-2">
                                  <Smartphone className="h-4 w-4" />
                                  {device.name}
                                </div>
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Preview */}
              <div className="space-y-6">
                <MobileChatPreview message={message} />
              </div>
            </div>
          </TabsContent>

          {/* Recipients Tab */}
          <TabsContent value="recipients" className="space-y-6">
            {/* Recipient Summary */}
            <Card>
              <CardContent className="pt-6">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-blue-900">
                        {t("form.recipient_summary.final_count", { count: finalRecipientCount })}
                      </p>
                      {selectedTags.length === 0 ? (
                        <p className="text-sm text-blue-700">
                          {t("form.recipient_summary.all_contacts", { count: totalRecipients })}
                        </p>
                      ) : (
                        <p className="text-sm text-blue-700">
                          {t("form.recipient_summary.tagged_contacts", {
                            count: availableTags
                              .filter(tagObj => selectedTags.includes(tagObj.tag))
                              .reduce((sum, tagObj) => sum + tagObj.count, 0)
                          })}
                        </p>
                      )}
                      {selectedTags.length > 0 && (
                        <p className="text-sm text-blue-600">
                          {t("form.recipient_summary.selected_tags", { tags: selectedTags.join(", ") })}
                        </p>
                      )}
                      {excludedContactIds.length > 0 && (
                        <p className="text-sm text-red-600">
                          {t("form.recipient_summary.excluding_contacts", {
                            count: selectedTags.length === 0
                              ? excludedContactIds.length
                              : contacts.filter(contact =>
                                excludedContactIds.includes(contact.id) &&
                                contact.tags.some(tag => selectedTags.includes(tag))
                              ).length
                          })}
                        </p>
                      )}
                    </div>
                    <Badge variant="default" className="text-lg px-3 py-1">
                      {finalRecipientCount}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tag Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="h-5 w-5" />
                  {t("form.sections.tag_selection")}
                </CardTitle>
                <CardDescription>
                  {t("form.hints.tag_selection")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>{t("form.fields.tags")}</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {availableTags.map((tagObj) => (
                      <Badge
                        key={tagObj.tag}
                        variant={selectedTags.includes(tagObj.tag) ? "default" : "outline"}
                        className="cursor-pointer hover:bg-primary/80 flex items-center gap-2"
                        onClick={() => handleTagToggle(tagObj.tag)}
                      >
                        <span>{tagObj.tag}</span>
                        <span className="text-xs opacity-70">
                          ({tagObj.count})
                        </span>
                        {selectedTags.includes(tagObj.tag) && (
                          <Check className="h-3 w-3" />
                        )}
                      </Badge>
                    ))}
                    {availableTags.length === 0 && (
                      <p className="text-sm text-muted-foreground">{t("form.empty.tags")}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Exclusion */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t("form.sections.contact_selection")}
                </CardTitle>
                <CardDescription>
                  {t("form.hints.contact_selection")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t("form.placeholders.search_contacts")}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {loading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                    <p className="mt-2 text-sm text-muted-foreground">{t("form.loading.contacts")}</p>
                  </div>
                ) : (
                  <div className="max-h-64 overflow-y-auto space-y-2">
                    {contacts.map((contact) => {
                      const isExcluded = excludedContactIds.includes(contact.id)
                      const shouldBeIncluded = shouldContactBeIncluded(contact)
                      const buttonAction = getContactButtonAction(contact)

                      return (
                        <div
                          key={contact.id}
                          className={`flex items-center justify-between p-3 border rounded-lg ${isExcluded
                            ? "bg-red-50 border-red-200"
                            : shouldBeIncluded
                              ? "bg-green-50 border-green-200"
                              : "bg-gray-50 border-gray-200"
                            }`}
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <p className="font-medium text-sm">{contact.name}</p>
                              {contact.tags && contact.tags.length > 0 && (
                                <div className="flex gap-1">
                                  {contact.tags.map((tag: string) => (
                                    <Badge
                                      key={tag}
                                      variant={selectedTags.includes(tag) ? "default" : "secondary"}
                                      className="text-xs"
                                    >
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground">{contact.phone}</p>
                          </div>
                          <Button
                            type="button"
                            variant={buttonAction === "include" ? "default" : "outline"}
                            size="sm"
                            onClick={() => handleContactExclude(contact.id)}
                          >
                            {buttonAction === "include" ? (
                              <>
                                <Check className="h-3 w-3 mr-1" />
                                {t("form.buttons.include")}
                              </>
                            ) : (
                              <>
                                <X className="h-3 w-3 mr-1" />
                                {t("form.buttons.exclude")}
                              </>
                            )}
                          </Button>
                        </div>
                      )
                    })}
                    {contacts.length === 0 && !loading && (
                      <p className="text-sm text-muted-foreground text-center py-4">
                        {t("form.empty.contacts")}
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      {/* Submit Button */}
      <div className="flex justify-end gap-4 pt-6 bottom-0 sticky bg-white border-t border-gray-200">
        <Button type="button" variant="outline" onClick={onCancel}>
          {t("form.buttons.cancel")}
        </Button>
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={
            isSubmitting ||
            !broadcastTitle.trim() ||
            !message.trim() ||
            !selectedDeviceId
          }
          className="flex items-center gap-2"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {t("form.buttons.saving")}
            </>
          ) : (
            <>
              <Send className="h-4 w-4" />
              {submitButtonText}
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
