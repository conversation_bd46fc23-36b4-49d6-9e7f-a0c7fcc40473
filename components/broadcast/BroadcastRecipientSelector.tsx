// "use client"

// import { Badge } from "@/components/ui/badge"
// import { Button } from "@/components/ui/button"
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
// import { Input } from "@/components/ui/input"
// import { BroadcastRecipientsAPI } from "@/lib/services"
// import type { BroadcastRecipientContact, TagWithCount } from "@/lib/services/broadcastRecipientsApi"
// import { Check, Search, Tag, Users, X } from "lucide-react"
// import { useEffect, useState } from "react"

// interface BroadcastRecipientSelectorProps {
//   selectedTags: string[]
//   excludedContactIds: string[]
//   onTagsChange: (tags: string[]) => void
//   onExcludedContactsChange: (contactIds: string[]) => void
//   onRecipientCountChange: (count: number) => void
// }

// export function BroadcastRecipientSelector({
//   selectedTags,
//   excludedContactIds,
//   onTagsChange,
//   onExcludedContactsChange,
//   onRecipientCountChange,
// }: BroadcastRecipientSelectorProps) {
//   const [availableTags, setAvailableTags] = useState<TagWithCount[]>([])
//   const [contacts, setContacts] = useState<BroadcastRecipientContact[]>([])
//   const [searchTerm, setSearchTerm] = useState("")
//   const [currentPage, setCurrentPage] = useState(1)
//   const [totalPages, setTotalPages] = useState(1)
//   const [totalRecipients, setTotalRecipients] = useState(0)
//   const [loading, setLoading] = useState(false)

//   // Load available tags on component mount
//   useEffect(() => {
//     const loadAvailableTags = async () => {
//       try {
//         const response = await BroadcastRecipientsAPI.GetAvailableTags().request()
//         setAvailableTags(response.availableTags || [])
//       } catch (error) {
//         console.error("Failed to load available tags:", error)
//       }
//     }
//     loadAvailableTags()
//   }, [])

//   // Load contacts when filters change
//   useEffect(() => {
//     const loadContacts = async () => {
//       setLoading(true)
//       try {
//         const response = await BroadcastRecipientsAPI.All({
//           page: currentPage,
//           per_page: 20,
//           search: searchTerm || undefined,
//           // tags: selectedTags.length > 0 ? selectedTags : undefined,
//           // excludedIds: excludedContactIds.length > 0 ? excludedContactIds : undefined,
//         }).request()

//         setContacts(response.contacts || [])
//         setTotalPages(response.totalPages || 1)
//         setTotalRecipients(response.selectedCount || 0)
//         onRecipientCountChange(response.selectedCount || 0)
//       } catch (error) {
//         console.error("Failed to load contacts:", error)
//         setContacts([])
//         setTotalRecipients(0)
//         onRecipientCountChange(0)
//       } finally {
//         setLoading(false)
//       }
//     }

//     loadContacts()
//   }, [selectedTags, excludedContactIds, searchTerm, currentPage])

//   const handleTagToggle = (tag: string) => {
//     const newTags = selectedTags.includes(tag)
//       ? selectedTags.filter(t => t !== tag)
//       : [...selectedTags, tag]
//     onTagsChange(newTags)
//     setCurrentPage(1) // Reset to first page when filters change
//   }

//   const handleContactExclude = (contactId: string) => {
//     const newExcluded = excludedContactIds.includes(contactId)
//       ? excludedContactIds.filter(id => id !== contactId)
//       : [...excludedContactIds, contactId]
//     onExcludedContactsChange(newExcluded)
//   }

//   const clearAllFilters = () => {
//     onTagsChange([])
//     onExcludedContactsChange([])
//     setSearchTerm("")
//     setCurrentPage(1)
//   }

//   return (
//     <div className="space-y-6">
//       {/* Tag Selection */}
//       <Card>
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <Tag className="h-5 w-5" />
//             Select Contact Tags
//           </CardTitle>
//           <CardDescription>
//             Choose tags to include contacts. Multiple tags will include contacts with any of the selected tags.
//           </CardDescription>
//         </CardHeader>
//         <CardContent>
//           <div className="flex flex-wrap gap-2">
//             {availableTags.map((tag) => (
//               <Badge
//                 key={tag.tag}
//                 variant={selectedTags.includes(tag.tag) ? "default" : "outline"}
//                 className="cursor-pointer hover:bg-primary/80"
//                 onClick={() => handleTagToggle(tag.tag)}
//               >
//                 {tag.tag}
//                 {selectedTags.includes(tag.tag) && (
//                   <Check className="h-3 w-3 ml-1" />
//                 )}
//               </Badge>
//             ))}
//             {availableTags.length === 0 && (
//               <p className="text-sm text-muted-foreground">No tags available</p>
//             )}
//           </div>
//           {selectedTags.length > 0 && (
//             <div className="mt-4 p-3 bg-blue-50 rounded-lg">
//               <p className="text-sm font-medium text-blue-900">
//                 Selected Tags: {selectedTags.join(", ")}
//               </p>
//               <p className="text-sm text-blue-700">
//                 This will include {totalRecipients} contacts
//               </p>
//             </div>
//           )}
//         </CardContent>
//       </Card>

//       {/* Contact Exclusion */}
//       <Card>
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <Users className="h-5 w-5" />
//             Exclude Specific Contacts
//           </CardTitle>
//           <CardDescription>
//             Search and exclude specific contacts from the broadcast
//           </CardDescription>
//         </CardHeader>
//         <CardContent>
//           <div className="space-y-4">
//             <div className="flex gap-2">
//               <div className="relative flex-1">
//                 <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
//                 <Input
//                   placeholder="Search contacts..."
//                   value={searchTerm}
//                   onChange={(e) => setSearchTerm(e.target.value)}
//                   className="pl-10"
//                 />
//               </div>
//               <Button
//                 variant="outline"
//                 onClick={clearAllFilters}
//                 disabled={selectedTags.length === 0 && excludedContactIds.length === 0 && !searchTerm}
//               >
//                 Clear All
//               </Button>
//             </div>

//             {loading ? (
//               <div className="text-center py-8">
//                 <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
//                 <p className="mt-2 text-sm text-muted-foreground">Loading contacts...</p>
//               </div>
//             ) : (
//               <div className="space-y-2 max-h-96 overflow-y-auto">
//                 {contacts.map((contact) => {
//                   const isExcluded = excludedContactIds.includes(contact.id)
//                   return (
//                     <div
//                       key={contact.id}
//                       className={`flex items-center justify-between p-3 border rounded-lg ${isExcluded ? "bg-red-50 border-red-200" : "bg-white"
//                         }`}
//                     >
//                       <div className="flex-1">
//                         <div className="flex items-center gap-2">
//                           <p className="font-medium">{contact.name}</p>
//                           {contact.tags && contact.tags.length > 0 && (
//                             <div className="flex gap-1">
//                               {contact.tags.map((tag) => (
//                                 <Badge key={tag} variant="secondary" className="text-xs">
//                                   {tag}
//                                 </Badge>
//                               ))}
//                             </div>
//                           )}
//                         </div>
//                         <p className="text-sm text-muted-foreground">{contact.phone}</p>
//                         {contact.email && (
//                           <p className="text-sm text-muted-foreground">{contact.email}</p>
//                         )}
//                       </div>
//                       <Button
//                         variant={isExcluded ? "default" : "outline"}
//                         size="sm"
//                         onClick={() => handleContactExclude(contact.id)}
//                       >
//                         {isExcluded ? (
//                           <>
//                             <Check className="h-4 w-4 mr-1" />
//                             Include
//                           </>
//                         ) : (
//                           <>
//                             <X className="h-4 w-4 mr-1" />
//                             Exclude
//                           </>
//                         )}
//                       </Button>
//                     </div>
//                   )
//                 })}
//                 {contacts.length === 0 && !loading && (
//                   <div className="text-center py-8">
//                     <Users className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
//                     <p className="text-muted-foreground">No contacts found</p>
//                   </div>
//                 )}
//               </div>
//             )}

//             {/* Pagination */}
//             {totalPages > 1 && (
//               <div className="flex justify-center gap-2 pt-4">
//                 <Button
//                   variant="outline"
//                   size="sm"
//                   onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
//                   disabled={currentPage === 1}
//                 >
//                   Previous
//                 </Button>
//                 <span className="flex items-center px-3 text-sm">
//                   Page {currentPage} of {totalPages}
//                 </span>
//                 <Button
//                   variant="outline"
//                   size="sm"
//                   onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
//                   disabled={currentPage === totalPages}
//                 >
//                   Next
//                 </Button>
//               </div>
//             )}
//           </div>
//         </CardContent>
//       </Card>

//       {/* Summary */}
//       {(selectedTags.length > 0 || excludedContactIds.length > 0) && (
//         <Card>
//           <CardContent className="pt-6">
//             <div className="flex items-center justify-between">
//               <div>
//                 <p className="font-medium">Broadcast Summary</p>
//                 <p className="text-sm text-muted-foreground">
//                   {totalRecipients} contacts will receive this broadcast
//                 </p>
//               </div>
//               <Badge variant="default" className="text-lg px-3 py-1">
//                 {totalRecipients}
//               </Badge>
//             </div>
//           </CardContent>
//         </Card>
//       )}
//     </div>
//   )
// }
