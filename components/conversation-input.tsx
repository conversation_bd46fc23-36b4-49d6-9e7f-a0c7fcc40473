"use client"

import { useRef, useState } from "react"
import {
  Send,
  Paperclip,
  Smile,
  LayoutTemplateIcon as Template,
  Clock,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useSendTyping } from "@/hooks/useSendTyping"
import { useConversationMessage } from "@/hooks/useConversationMessage"
import { locales } from "./locales"
import { useLocalization } from "@/localization/functions/client"
import { Conversation } from "@/lib/repositories/conversations"
import { EmojiPicker } from "@/components/emoji-picker"

interface ConversationInputProps {
  onSendMessage: (message: string) => void
  onToggleTemplates: () => void
  showTemplates: boolean
  conversation: Conversation
  messageInput: string
  setMessageInput: (message: string) => void
}

export function ConversationInput({
  onSendMessage,
  onToggleTemplates,
  showTemplates,
  conversation,
  messageInput: message,
  setMessageInput: setMessage,
}: ConversationInputProps) {
  const { sendTyping, loading, response, error } = useSendTyping()
  const { t } = useLocalization("chatInput", locales)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const isTypingRef = useRef(false) // Tracks if we already sent `isStart: true`

  // Dialog and popover states
  const [showAttachmentDialog, setShowAttachmentDialog] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleSend = () => {
    const trimmed = message.trim()
    if (!trimmed) return
    onSendMessage(trimmed)
    setMessage("")
    textareaRef.current!.style.height = "auto"
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  // Emoji picker handler
  const handleEmojiSelect = (emoji: string) => {
    const textarea = textareaRef.current
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newMessage = message.slice(0, start) + emoji + message.slice(end)
      setMessage(newMessage)

      // Set cursor position after emoji
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + emoji.length, start + emoji.length)
      }, 0)
    } else {
      setMessage(message + emoji)
    }
    setShowEmojiPicker(false)
  }

  // Attachment handler
  const handleAttachmentClick = () => {
    setShowAttachmentDialog(true)
  }

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }

  const handleTyping = (value: string) => {
    setMessage(value)
    adjustTextareaHeight()

    // Only trigger once per typing session
    if (!isTypingRef.current && value.trim()) {
      sendTyping({
        status: "start",
        conversationId: conversation.id,
      })
      isTypingRef.current = true
    }

    // Clear previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // If user stops typing for 2 seconds, send stop-typing
    timeoutRef.current = setTimeout(() => {
      if (isTypingRef.current) {
        sendTyping({
          status: "stop",
          conversationId: conversation.id,
        })
        isTypingRef.current = false
      }
    }, 2000)
  }

  return (
    <div className="border-t bg-background p-4">
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Separator orientation="vertical" className="h-6" />

            <div className="flex items-center gap-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" onClick={handleAttachmentClick}>
                    <Paperclip className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{t("attachFile")}</TooltipContent>
              </Tooltip>

              <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <Smile className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <EmojiPicker onEmojiSelect={handleEmojiSelect} />
                </PopoverContent>
              </Popover>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onToggleTemplates}
                    className={showTemplates ? "bg-accent" : ""}
                  >
                    <Template className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{t("templates")}</TooltipContent>
              </Tooltip>
            </div>
          </TooltipProvider>
        </div>

        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              placeholder={t("placeholder")}
              value={message}
              onChange={(e) => handleTyping(e.target.value)}
              onKeyDown={handleKeyPress}
              className="min-h-10 max-h-32 resize-none pr-12"
              rows={1}
            />
          </div>
          <Button
            onClick={handleSend}
            disabled={!message.trim()}
            className="self-end"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-xs text-muted-foreground">{t("pressEnter")}</div>
      </div>

      {/* Attachment Coming Soon Dialog */}
      <Dialog open={showAttachmentDialog} onOpenChange={setShowAttachmentDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-500" />
              {t("attachmentComingSoon")}
            </DialogTitle>
            <DialogDescription>
              {t("attachmentComingSoonDescription")}
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end">
            <Button onClick={() => setShowAttachmentDialog(false)}>
              {t("understood")}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
