"use client"

import { AccountInfo } from "@/lib/repositories/account"
import { AuthAPI } from "@/lib/services"
import { ApiService } from "@/lib/services/apiService"
import { cn } from "@/lib/utils"
import { secureStorage, StorageKeys } from "@/lib/utils/SecureStorage"
import { useLocalization } from "@/localization/functions/client"
import {
  Book,
  Brain,
  ChevronsLeft,
  ChevronsRight,
  Database,
  Headphones,
  LayoutDashboard,
  LayoutTemplate,
  MessageSquare,
  Radio,
  Settings,
  Smartphone,
  User,
  WashingMachine,
  X
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Fragment, useEffect, useState } from "react"
import { AccountInfoProvider } from "../contexts/useAccountContext"
import { DashboardHeader } from "../dashboard-header"
import { locales } from "./locales"
import { TestChatFAB } from "../ai-test-chat"

const getNavItems = (t: (key: string) => string) => [
  {
    name: t("dashboard_client_layout.navigation.items.dashboard"),
    href: "/dashboard",
    icon: LayoutDashboard,
    group: t("dashboard_client_layout.navigation.groups.dashboard"),
  },
  {
    name: t("dashboard_client_layout.navigation.items.conversations"),
    href: "/conversations",
    icon: MessageSquare,
    group: t("dashboard_client_layout.navigation.groups.crm"),
  },
  {
    name: t("dashboard_client_layout.navigation.items.contacts"),
    href: "/contacts",
    icon: User,
    group: t("dashboard_client_layout.navigation.groups.crm")
  },
  {
    name: t("dashboard_client_layout.navigation.items.broadcast"),
    href: "/broadcast",
    icon: Radio,
    group: t("dashboard_client_layout.navigation.groups.crm")
  },
  {
    name: t("dashboard_client_layout.navigation.items.message_templates"),
    href: "/message-templates",
    icon: LayoutTemplate,
    group: t("dashboard_client_layout.navigation.groups.crm"),
  },
  {
    name: t("dashboard_client_layout.navigation.items.test_chat"),
    component: TestChatFAB,
    icon: MessageSquare,
    group: t("dashboard_client_layout.navigation.groups.ai"),
  },
  {
    name: t("dashboard_client_layout.navigation.items.knowledge_base"),
    href: "/knowledge-base",
    icon: Book,
    group: t("dashboard_client_layout.navigation.groups.ai")
  },
  {
    name: t("dashboard_client_layout.navigation.items.ai_rules"),
    href: "/ai-rules",
    icon: Brain,
    group: t("dashboard_client_layout.navigation.groups.ai")
  },
  {
    name: t("dashboard_client_layout.navigation.items.ai_executions"),
    href: "/ai-workflow-executions",
    icon: WashingMachine,
    group: t("dashboard_client_layout.navigation.groups.ai"),
  },
  {
    name: t("dashboard_client_layout.navigation.items.ai_data_sources"),
    href: "/datasources",
    icon: Database,
    group: t("dashboard_client_layout.navigation.groups.system"),
  },
  {
    name: t("dashboard_client_layout.navigation.items.devices"),
    href: "/devices",
    icon: Smartphone,
    group: t("dashboard_client_layout.navigation.groups.system")
  },
  {
    name: t("dashboard_client_layout.navigation.items.settings"),
    href: "/settings/account",
    icon: Settings,
    group: t("dashboard_client_layout.navigation.groups.system"),
  },
  {
    name: t("dashboard_client_layout.navigation.items.support"),
    href: "/support",
    icon: Headphones,
    group: t("dashboard_client_layout.navigation.groups.support")
  },
]

function Sidebar({
  isOpen,
  onClose,
  isCompact,
  toggleCompact,
  t,
}: {
  isOpen: boolean
  onClose: () => void
  isCompact: boolean
  toggleCompact: () => void
  t: (key: string) => string
}) {
  const pathname = usePathname()
  const navItems = getNavItems(t)
  const groupedNav = navItems.reduce<Record<string, typeof navItems>>(
    (acc, item) => {
      acc[item.group] = acc[item.group] || []
      acc[item.group].push(item)
      return acc
    },
    {},
  )

  return (
    <aside
      className={cn(
        "fixed md:static z-40 bg-gray-50 border-r transition-all duration-300 ease-in-out h-screen overflow-y-auto p-4",
        isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
        isCompact ? "w-16" : "w-64",
      )}
    >
      <div className="flex items-center justify-between mb-4">
        <h1
          className={cn(
            "text-lg font-semibold transition-all",
            isCompact && "hidden",
          )}
        >
          {t("dashboard_client_layout.title")}
        </h1>
        <button
          onClick={toggleCompact}
          className="text-gray-600 hover:text-black hidden md:block"
        >
          {isCompact ? (
            <ChevronsRight className="h-5 w-5" />
          ) : (
            <ChevronsLeft className="h-5 w-5" />
          )}
        </button>
        <button
          onClick={onClose}
          className="md:hidden text-gray-600 hover:text-black"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <nav className="space-y-4">
        {Object.entries(groupedNav).map(([group, items]) => (
          <Fragment key={group}>
            {!isCompact && (
              <div className="text-xs font-semibold text-gray-500 px-2 uppercase tracking-wide">
                {group}
              </div>
            )}
            <div className="space-y-1">
              {items.map((item) => {
                if (item.component) {
                  return (
                    <div key={item.name}>
                      {<item.component isCompact={isCompact} />}
                    </div>
                  )
                }
                if (!item.href) { return null }
                const isActive = pathname.startsWith(item.href)
                return (
                  <div
                    key={item.href}
                    title={isCompact ? item.name : undefined}
                  >
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center gap-2 rounded px-3 py-2 text-sm font-medium transition-all",
                        isActive
                          ? "bg-gray-200 text-black"
                          : "text-gray-600 hover:bg-gray-100 hover:text-black",
                        isCompact && "justify-center px-2",
                      )}
                    >
                      <item.icon className="w-5 h-5" />
                      {!isCompact && <span>{item.name}</span>}
                    </Link>
                  </div>
                )
              })}
            </div>
          </Fragment>
        ))}
      </nav>
    </aside>
  )
}

export default function DashboardClientLayout({
  children,
  account
}: {
  children: React.ReactNode,
  account: AccountInfo
}) {
  const { t } = useLocalization("layout", locales)

  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [isCompact, setIsCompact] = useState(false)

  const performLogout = async (redirectUrl: string) => {
    try {
      await AuthAPI.Logout({}).request()
      window.location.href = redirectUrl
    } catch (err) {
      console.error(t("dashboard_client_layout.errors.logout_failed"), err)
    } finally {
      secureStorage.removeItem(StorageKeys.CurrentOrganization)
    }
  }

  useEffect(() => {
    ApiService.getFunctions().registerRefreshTokenFunction(async () => {
      try {
        const { success } = await AuthAPI.RefreshToken({}).request()
        if (!success) {
          throw new Error(t("dashboard_client_layout.errors.refresh_token_failed"))
        }
      } catch (error) {
        await performLogout("/auth/login")
      }
    })
  }, [])

  const handleLogout = () => {
    performLogout("/")
  }

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      <AccountInfoProvider account={account}>
        <Sidebar
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          isCompact={isCompact}
          toggleCompact={() => setIsCompact((prev) => !prev)}
          t={t}
        />
        <main className="flex flex-col w-full max-h-screen overflow-y-auto">
          <DashboardHeader handleLogout={handleLogout} account={account} />
          {children}
        </main>
      </AccountInfoProvider>
    </div>
  )
}
