"use client"

import { useState } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { ChevronsLeft, ChevronsRight, FileText, FileUp } from "lucide-react"
import { cn } from "@/lib/utils"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

const navItems = [
  { name: "Text Entry", href: "/knowledge-base/text", icon: FileText },
  // { name: "Upload Documents", href: "/knowledge-base/documents", icon: FileUp },
]

function Sidebar({
  isOpen,
  onClose,
  isCompact,
  toggleCompact,
  t,
}: {
  isOpen: boolean
  onClose: () => void
  isCompact: boolean
  toggleCompact: () => void
  t: (key: string) => string
}) {
  const pathname = usePathname()

  return (
    <aside
      className={cn(
        "fixed md:static z-40 bg-gray-50 border-r transition-all duration-300 ease-in-out h-screen overflow-y-auto p-4",
        isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
        isCompact ? "w-16" : "w-48",
      )}
    >
      <div className="flex items-center justify-between mb-6">
        <h1
          className={cn(
            "text-lg font-semibold transition-all",
            isCompact && "hidden",
          )}
        >
          {t("knowledge_base_client_layout.knowledge_base")}
        </h1>
        <button
          onClick={toggleCompact}
          className="text-gray-600 hover:text-black hidden md:block"
          aria-label={isCompact ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCompact ? (
            <ChevronsRight className="h-5 w-5" />
          ) : (
            <ChevronsLeft className="h-5 w-5" />
          )}
        </button>
        <button
          onClick={onClose}
          className="md:hidden text-gray-600 hover:text-black"
          aria-label="Close sidebar"
        >
          ×
        </button>
      </div>

      <nav className="space-y-2">
        {navItems.map(({ name, href, icon: Icon }) => {
          const isActive = pathname.startsWith(href)
          return (
            <Link
              key={href}
              href={href}
              className={cn(
                "flex items-center gap-3 rounded px-3 py-2 text-sm font-medium transition-colors",
                isActive
                  ? "bg-gray-200 text-black"
                  : "text-gray-600 hover:bg-gray-100 hover:text-black",
                isCompact && "justify-center px-2",
              )}
              title={isCompact ? name : undefined}
            >
              <Icon className="w-5 h-5" />
              {!isCompact && <span>{name}</span>}
            </Link>
          )
        })}
      </nav>
    </aside>
  )
}

export default function KnowledgeBaseClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { t } = useLocalization("layout", locales)

  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [isCompact, setIsCompact] = useState(false)

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        isCompact={isCompact}
        toggleCompact={() => setIsCompact((prev) => !prev)}
        t={t}
      />

      <main className="flex-1 md:ml-0 ml-0 md:pl-6 p-4">
        <button
          onClick={() => setSidebarOpen(true)}
          className="md:hidden mb-4 inline-flex items-center gap-2 text-gray-600 hover:text-black"
          aria-label="Open menu"
        >
          ☰ {t("knowledge_base_client_layout.menu")}
        </button>
        {children}
      </main>
    </div>
  )
}
