"use client"

import { useState, useEffect } from "react"
import { useLocalization } from "@/localization/functions/client"
import { knowledgeBaseLocales } from "./locales"

import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Loader2 } from "lucide-react"

import {
  KnowledgeBaseAPI,
  KnowledgeBaseEntry,
} from "@/lib/services/knowledgeBaseApi"

export default function KnowledgeBaseInputPage() {
  const { t } = useLocalization("knowledgeBase", knowledgeBaseLocales)

  const [content, setContent] = useState("")
  const [message, setMessage] = useState("")
  const [isStale, setIsStale] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Track if we have an existing knowledge base (one per account)
  const [existingEntry, setExistingEntry] = useState<KnowledgeBaseEntry | null>(
    null,
  )
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const [kbStrength, setKbStrength] = useState<number>(0)
  const [totalEntries, setTotalEntries] = useState<number>(0)
  const [keywords, setKeywords] = useState<string[]>([])
  const [rules, setRules] = useState<string[]>([])
  const [templates, setTemplates] = useState<string[]>([])

  const fetchAnalytics = async () => {
    try {
      const res = await KnowledgeBaseAPI.GetAllStats().request()
      setKbStrength(res.score || 0)
      setTotalEntries(res.totalEntries || 0)
      setKeywords(res.keywords || [])
      setRules(res.inferredRules || [])
      setTemplates(res.suggestedTemplates || [])
    } catch (error) {
      console.error("Failed to fetch KB stats", error)
    }
  }

  const loadExistingKnowledgeBase = async () => {
    try {
      setLoading(true)
      // Get the user's single knowledge base (first entry for this organization)
      const res = await KnowledgeBaseAPI.GetAllEntries({
        limit: 1,
        page: 1,
      }).request()

      if (res.items && res.items.length > 0) {
        const entry = res.items[0]
        setExistingEntry(entry)
        setContent(entry.content)
        setHasUnsavedChanges(false)
      } else {
        // No existing knowledge base found, start fresh
        setExistingEntry(null)
        setContent("")
        setHasUnsavedChanges(false)
      }
    } catch (error) {
      console.error("Failed to load existing knowledge base", error)
      // If loading fails, start fresh
      setExistingEntry(null)
      setContent("")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadExistingKnowledgeBase()
    fetchAnalytics()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!content.trim()) {
      setMessage(t("knowledgeBase.content_empty_error"))
      return
    }

    try {
      setSaving(true)
      setMessage("")

      let result: KnowledgeBaseEntry

      console.log("Existing entry", existingEntry)

      if (existingEntry) {
        // Update the existing knowledge base
        result = await KnowledgeBaseAPI.UpdateEntry(existingEntry.id, {
          title: "Knowledge Base",
          content,
          category: "general",
          tags: ["knowledge-base"],
        }).request()
        setMessage(t("knowledgeBase.update_success"))
      } else {
        // Create the user's first knowledge base
        result = await KnowledgeBaseAPI.CreateEntry({
          title: "Knowledge Base",
          content,
          category: "general",
          tags: ["knowledge-base"],
        }).request()
        setMessage(t("knowledgeBase.save_success"))
        setExistingEntry(result) // Now we have the knowledge base for future updates
      }

      setHasUnsavedChanges(false)
      setIsStale(false)
      await fetchAnalytics()
    } catch (err: any) {
      console.error("Error saving knowledge base:", err)
      setMessage(err.message || t("knowledgeBase.save_error"))
    } finally {
      setSaving(false)
    }
  }

  const handleContentChange = (value: string) => {
    setContent(value)
    setHasUnsavedChanges(true)
    setIsStale(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>{t("knowledgeBase.loading")}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col lg:flex-row max-w-6xl mx-auto mt-20 p-6 gap-8">
      {/* Left - Input Form */}
      <div className="flex-1 bg-white shadow-md rounded-md p-6 space-y-6">
        <div>
          <h1 className="text-2xl font-bold">{t("knowledgeBase.title")}</h1>
          <p className="text-gray-600">{t("knowledgeBase.description")}</p>

          {existingEntry && (
            <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700">
              {t("knowledgeBase.editing_info", {
                date: new Date(existingEntry.updatedAt).toLocaleDateString()
              })}
            </div>
          )}
          {hasUnsavedChanges && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700">
              {t("knowledgeBase.unsaved_changes")}
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="knowledge-content">
              {t("knowledgeBase.label")}
            </Label>
            <Textarea
              id="knowledge-content"
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              className="h-96 resize-none"
              placeholder={t("knowledgeBase.placeholder")}
              disabled={saving}
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={saving || !content.trim()}
          >
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {existingEntry ? t("knowledgeBase.updating") : t("knowledgeBase.saving")}
              </>
            ) : existingEntry ? (
              t("knowledgeBase.update_button")
            ) : (
              t("knowledgeBase.save_button")
            )}
          </Button>

          {message && (
            <p
              className={`text-sm pt-2 ${message.includes("success") || message.includes("updated")
                ? "text-green-600"
                : "text-red-600"
                }`}
            >
              {message}
            </p>
          )}
        </form>
      </div>

      {/* Right - Sidebar Analytics */}
      <div hidden className="w-full lg:w-80 p-4 bg-gray-50 rounded-md border space-y-6 relative">
        {isStale && (
          <div className="absolute top-0 left-0 w-full bg-yellow-100 text-yellow-800 text-sm p-2 border-b border-yellow-300 rounded-t-md">
            {t("knowledgeBase.stale_warning")}
          </div>
        )}

        <div className={isStale ? "pt-8" : ""}>
          <h2 className="font-semibold text-lg">
            {t("knowledgeBase.kb_strength")}
          </h2>
          <p className="text-sm text-gray-600 mb-2">
            {t("knowledgeBase.kb_strength_desc")}
          </p>
          <Progress value={kbStrength} className="h-3" />
          <p className="text-sm text-gray-700 mt-1">
            {t("knowledgeBase.entries_count", { strength: kbStrength, count: totalEntries })}
          </p>
        </div>

        <div>
          <h3 className="font-semibold">{t("knowledgeBase.keywords")}</h3>
          <div className="flex flex-wrap gap-2 mt-2">
            {keywords.map((word, idx) => (
              <Badge key={idx} variant="secondary">
                {word}
              </Badge>
            ))}
          </div>
        </div>

        <div>
          <h3 className="font-semibold">{t("knowledgeBase.inferred_rules")}</h3>
          <ul className="list-disc pl-5 mt-2 text-sm text-gray-700 space-y-1">
            {rules.map((rule, idx) => (
              <li key={idx}>{rule}</li>
            ))}
          </ul>
        </div>

        <div>
          <h3 className="font-semibold">
            {t("knowledgeBase.suggested_templates")}
          </h3>
          <ul className="list-disc pl-5 mt-2 text-sm text-gray-700 space-y-1">
            {templates.map((template, idx) => (
              <li key={idx}>{template}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}
