"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { MessageTemplatesAPI } from "@/lib/services/messageTemplatesApi"
import { MessageTemplate } from "@/lib/repositories/messageTemplates/interface"
import { useLocalization } from "@/localization/functions/client"
import { messageTemplatesLocales } from "../locales"

interface ClientPageProps {
  id: string
}

export default function ClientPage({ id }: ClientPageProps) {
  const { t } = useLocalization("message-templates", messageTemplatesLocales)

  const messageTemplateEditorConfig: DataEditorConfig = {
    title: t('title'),
    subtitle: t('subtitle'),

    fields: [
      {
        name: "title",
        label: t('fields.name.label'),
        type: "text",
        placeholder: t('fields.name.placeholder'),
        description: `${t('fields.name.description')}\n\n${t('fields.name.examples')}`,
        validation: {
          required: true,
          minLength: 2,
          maxLength: 100,
        },
        group: "basic",
      },
      {
        name: "query",
        label: t('fields.query.label'),
        type: "text",
        placeholder: t('fields.query.placeholder'),
        validation: {
          required: true,
          maxLength: 100,
        },
        group: "content",
      },
      {
        name: "template",
        label: t('fields.content.label'),
        type: "textarea",
        placeholder: t('fields.content.placeholder'),
        description: `${t('fields.content.description')}\n\n${t('fields.content.examples')}`,
        rows: 6,
        validation: {
          required: true,
          minLength: 5,
        },
        group: "content",
      },
      {
        name: "variables",
        label: t('fields.variables.label'),
        type: "text",
        placeholder: t('fields.variables.placeholder'),
        description: `${t('fields.variables.description')}\n\n${t('fields.variables.examples')}`,
        group: "content",
      },
      {
        name: "tags",
        label: t('fields.tags.label'),
        type: "text",
        placeholder: t('fields.tags.placeholder'),
        description: `${t('fields.tags.description')}\n\n${t('fields.tags.examples')}`,
        group: "meta",
      },
      {
        name: "description",
        label: t('fields.description.label'),
        type: "textarea",
        placeholder: t('fields.description.placeholder'),
        description: `${t('fields.description.description')}\n\n${t('fields.description.examples')}`,
        rows: 3,
        validation: {
          maxLength: 500,
        },
        group: "meta",
      },
      {
        name: "createdAt",
        label: t('fields.createdAt.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
      {
        name: "updatedAt",
        label: t('fields.updatedAt.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
      {
        name: "createdBy",
        label: t('fields.createdBy.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
    ],

    sections: [
      {
        title: t('sections.basic.title'),
        description: t('sections.basic.description'),
        fields: ["title", "category"],
      },
      {
        title: t('sections.content.title'),
        description: t('sections.content.description'),
        fields: ["query", "template", "variables"],
      },
      {
        title: t('sections.meta.title'),
        description: t('sections.meta.description'),
        fields: ["tags", "description"],
      },
      {
        title: t('sections.system.title'),
        description: t('sections.system.description'),
        fields: ["createdAt", "updatedAt", "createdBy"],
      },
    ],

    fetchData: async (id: string) => {
      try {
        const template = await MessageTemplatesAPI.Detail(id).request()

        if (!template) throw new Error(t('errors.notFound'))

        return {
          id: template.id,
          title: template.title,
          query: template.query,
          template: template.template,
          variables: template.variables?.join(", ") || "",
          tags: template.tags?.join(", ") || "",
          createdAt: template.createdAt
            ? new Date(template.createdAt).toLocaleString("id-ID")
            : "",
          updatedAt: template.updatedAt
            ? new Date(template.updatedAt).toLocaleString("id-ID")
            : "",
          createdBy: template.createdBy || t('fields.createdBy.default'),
        }
      } catch (error: any) {
        console.error("Error fetching message template:", error)
        throw new Error(error?.message || t('errors.fetchFailed'))
      }
    },

    saveData: async (data: Record<string, any>) => {
      try {
        const payload: Partial<MessageTemplate> = {
          title: data.title,
          query: data.query,
          template: data.template,
          variables: data.variables
            ? data.variables
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
            : [],
          tags: data.tags
            ? data.tags
              .split(",")
              .map((t: string) => t.trim())
              .filter(Boolean)
            : [],
          isActive: true,
        }

        await MessageTemplatesAPI.Update(data.id, payload).request()
      } catch (error: any) {
        console.error("Error updating message template:", error)

        let message = t('errors.updateFailed')
        if (error?.response?.data?.messages?.length > 0) {
          message = error.response.data.messages[0]
        } else if (error?.message) {
          message = error.message
        }

        throw new Error(message)
      }
    },

    backRoute: "/message-templates",
    successRoute: "/message-templates",

    submitButtonText: t('buttons.update'),
    cancelButtonText: t('buttons.cancel'),
    showImagePreview: false,
  }

  return <DataEditorPage config={messageTemplateEditorConfig} id={id} />
}
