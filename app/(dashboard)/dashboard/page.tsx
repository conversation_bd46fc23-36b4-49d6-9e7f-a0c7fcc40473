"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useLocalization } from "@/localization/functions/client"
import { dashboardLocales } from "./locales"
import { DevicesAPI } from "@/lib/services"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Smartphone,
  Brain,
  MessageCircle,
  CheckCircle,
  ArrowRight,
  Users,
  Database,
  Zap
} from "lucide-react"
import { YouTubeVideo } from "@/components/youtube-video"

interface OnboardingStep {
  id: string
  title: string
  description: string
  action: string
  icon: React.ReactNode
  href: string
  completed: boolean
}

interface DashboardStats {
  devicesConnected: number
  knowledgeItems: number
  activeConversations: number
}

export default function Dashboard() {
  const { t } = useLocalization("dashboard", dashboardLocales)
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats>({
    devicesConnected: 0,
    knowledgeItems: 0,
    activeConversations: 0
  })
  const [loading, setLoading] = useState(true)

  // Load actual dashboard stats
  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoading(true)

        // Load stats in parallel for better performance
        const [devicesResponse, knowledgeResponse, conversationsResponse] = await Promise.allSettled([
          // Devices API call
          DevicesAPI.All({ page: 1, per_page: 1 }).request().catch(() => ({ items: [], total: 0 })),
          // Knowledge Base API call
          fetch('/api/v1/knowledge-base?page=1&per_page=1').then(res => res.json()).catch(() => ({ data: { items: [], total: 0 } })),
          // Conversations API call
          fetch('/api/v1/conversations?page=1&per_page=1').then(res => res.json()).catch(() => ({ data: { items: [], total: 0 } }))
        ])

        const devicesCount = devicesResponse.status === 'fulfilled' && devicesResponse.value?.total ? devicesResponse.value.total : 0
        const knowledgeCount = knowledgeResponse.status === 'fulfilled' && knowledgeResponse.value?.data?.total ? knowledgeResponse.value.data.total : 0
        const conversationsCount = conversationsResponse.status === 'fulfilled' && conversationsResponse.value?.data?.total ? conversationsResponse.value.data.total : 0

        setStats({
          devicesConnected: devicesCount,
          knowledgeItems: knowledgeCount,
          activeConversations: conversationsCount
        })
      } catch (error) {
        console.error("Error loading dashboard stats:", error)
        // Set default values on error
        setStats({
          devicesConnected: 0,
          knowledgeItems: 0,
          activeConversations: 0
        })
      } finally {
        setLoading(false)
      }
    }

    loadStats()
  }, [])

  const steps: OnboardingStep[] = [
    {
      id: "device",
      title: t("step_1_title"),
      description: t("step_1_description"),
      action: t("step_1_action"),
      icon: <Smartphone className="h-8 w-8" />,
      href: "/devices",
      completed: stats.devicesConnected > 0
    },
    {
      id: "knowledge",
      title: t("step_2_title"),
      description: t("step_2_description"),
      action: t("step_2_action"),
      icon: <Brain className="h-8 w-8" />,
      href: "/knowledge-base",
      completed: stats.knowledgeItems > 0
    },
    {
      id: "conversations",
      title: t("step_3_title"),
      description: t("step_3_description"),
      action: t("step_3_action"),
      icon: <MessageCircle className="h-8 w-8" />,
      href: "/conversations",
      completed: stats.activeConversations > 0
    }
  ]

  const completedSteps = steps.filter(step => step.completed).length
  const progressPercentage = (completedSteps / steps.length) * 100
  const allStepsCompleted = completedSteps === steps.length

  const handleStepClick = (href: string) => {
    router.push(href)
  }

  return (
    <div className="container mx-auto py-8 px-60 w-full overflow-y-auto">
      {/* Header Section */}
      <div className="text-center mb-12">
        <div className="mb-6">
          {/* Hero Image/Logo */}
          <div className="mx-auto w-32 h-32 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-full flex items-center justify-center mb-4 shadow-2xl animate-pulse">
            <div className="relative">
              <Zap className="h-16 w-16 text-white drop-shadow-lg" />
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
              </div>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="flex justify-center space-x-2 mb-4">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">{t("welcome_title")}</h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">{t("welcome_subtitle")}</p>

        {/* Progress Bar */}
        <div className="max-w-md mx-auto mb-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>{completedSteps} of {steps.length} steps completed</span>
            <span>{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} className="h-3" />
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">{t("devices_connected")}</CardTitle>
            <div className="p-2 bg-blue-100 rounded-full">
              <Smartphone className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">
              {loading ? (
                <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
              ) : (
                <span className={stats.devicesConnected > 0 ? "text-green-600" : "text-gray-400"}>
                  {stats.devicesConnected}
                </span>
              )}
            </div>
            {stats.devicesConnected > 0 && (
              <div className="flex items-center mt-1">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                <span className="text-xs text-green-600 font-medium">Connected</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">{t("knowledge_items")}</CardTitle>
            <div className="p-2 bg-purple-100 rounded-full">
              <Database className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">
              {loading ? (
                <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
              ) : (
                <span className={stats.knowledgeItems > 0 ? "text-green-600" : "text-gray-400"}>
                  {stats.knowledgeItems}
                </span>
              )}
            </div>
            {stats.knowledgeItems > 0 && (
              <div className="flex items-center mt-1">
                <Brain className="w-3 h-3 text-purple-500 mr-1" />
                <span className="text-xs text-purple-600 font-medium">Ready</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">{t("active_conversations")}</CardTitle>
            <div className="p-2 bg-green-100 rounded-full">
              <Users className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">
              {loading ? (
                <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
              ) : (
                <span className={stats.activeConversations > 0 ? "text-green-600" : "text-gray-400"}>
                  {stats.activeConversations}
                </span>
              )}
            </div>
            {stats.activeConversations > 0 && (
              <div className="flex items-center mt-1">
                <MessageCircle className="w-3 h-3 text-green-500 mr-1" />
                <span className="text-xs text-green-600 font-medium">Active</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Learning Video Section */}
      <div className="mb-12">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{t("learning_video_title")}</h2>
          <p className="text-gray-600">{t("learning_video_description")}</p>
        </div>
        <div className="max-w-4xl mx-auto">
          <YouTubeVideo
            videoId="dQw4w9WgXcQ" // Replace with your actual video ID
            title={t("onboarding_video_title")}
            description={t("onboarding_video_description")}
            showDismiss={true}
          />
        </div>
      </div>

      {/* Setup Steps or Completion Message */}
      {allStepsCompleted ? (
        <Card className="text-center py-12 bg-gradient-to-br from-green-50 to-blue-50 border-green-200">
          <CardContent>
            <div className="mx-auto w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mb-6">
              <CheckCircle className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">{t("setup_complete_title")}</h2>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">{t("setup_complete_description")}</p>
            <Button onClick={() => router.push("/conversations")} size="lg">
              {t("step_3_action")} <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Getting Started</h2>
            <p className="text-gray-600">Complete these steps to unlock the full potential of CS AI</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {steps.map((step, index) => (
              <Card
                key={step.id}
                className={`relative transition-all duration-300 hover:shadow-xl hover:-translate-y-1 cursor-pointer group ${step.completed
                  ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 shadow-md'
                  : 'hover:border-blue-300 hover:bg-blue-50/30'
                  }`}
                onClick={() => handleStepClick(step.href)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className={`p-3 rounded-full ${step.completed
                      ? 'bg-green-500 text-white'
                      : 'bg-blue-100 text-blue-600'
                      }`}>
                      {step.completed ? <CheckCircle className="h-6 w-6" /> : step.icon}
                    </div>
                    <Badge variant={step.completed ? "default" : "secondary"}>
                      {step.completed ? t("step_completed") : t("step_pending")}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg">{step.title}</CardTitle>
                  <CardDescription className="text-sm">{step.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    variant={step.completed ? "outline" : "default"}
                    className="w-full"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleStepClick(step.href)
                    }}
                  >
                    {step.action} <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>

                {/* Step number */}
                <div className={`absolute -top-3 -left-3 w-8 h-8 border-2 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${step.completed
                  ? 'bg-green-500 border-green-500 text-white shadow-lg'
                  : 'bg-white border-gray-200 text-gray-600 group-hover:border-blue-300 group-hover:text-blue-600'
                  }`}>
                  {step.completed ? <CheckCircle className="h-4 w-4" /> : index + 1}
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
