"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { AiRulesAPI } from "@/lib/services/aiRulesApi"
import { AiRule } from "@/lib/repositories/aiRules/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

interface ClientPageProps {
  id: string
}

export default function ClientPage({ id }: ClientPageProps) {
  const { t } = useLocalization("ai-rules", locales)

  const aiRuleEditorConfig: DataEditorConfig = {
    title: t('title'),
    subtitle: t('subtitle'),

    fields: [
      {
        name: "name",
        label: t('fields.name.label'),
        type: "text",
        placeholder: t('fields.name.placeholder'),
        description: `${t('fields.name.description')}\n\n${t('fields.name.examples')}`,
        validation: {
          required: true,
          minLength: 2,
          maxLength: 100,
        },
        group: "basic",
      },
      {
        name: "description",
        label: t('fields.description.label'),
        type: "textarea",
        placeholder: t('fields.description.placeholder'),
        description: `${t('fields.description.description')}\n\n${t('fields.description.examples')}`,
        rows: 4,
        validation: {
          maxLength: 500,
        },
        group: "basic",
      },
      {
        name: "conditions",
        label: t('fields.conditions.label'),
        type: "textarea",
        placeholder: t('fields.conditions.placeholder'),
        description: `${t('fields.conditions.description')}\n\n${t('fields.conditions.examples')}`,
        validation: {
          required: true,
        },
        group: "logic",
      },
      {
        name: "actions",
        label: t('fields.actions.label'),
        type: "textarea",
        placeholder: t('fields.actions.placeholder'),
        description: `${t('fields.actions.description')}\n\n${t('fields.actions.examples')}`,
        validation: {
          required: true,
        },
        group: "logic",
      },
      {
        name: "tags",
        label: t('fields.tags.label'),
        type: "text",
        placeholder: t('fields.tags.placeholder'),
        description: `${t('fields.tags.description')}\n\n${t('fields.tags.examples')}`,
        group: "meta",
      },
      {
        name: "createdAt",
        label: t('fields.createdAt.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
      {
        name: "updatedAt",
        label: t('fields.updatedAt.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
      {
        name: "createdBy",
        label: t('fields.createdBy.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
    ],

    sections: [
      {
        title: t('sections.basicInfo.name'),
        description: t('sections.basicInfo.description'),
        fields: ["name", "description"],
      },
      {
        title: t('sections.ruleLogic.name'),
        description: t('sections.ruleLogic.description'),
        fields: ["conditions", "actions"],
      },
      {
        title: t('sections.metadata.name'),
        description: t('sections.metadata.description'),
        fields: ["tags", "isActive"],
      },
      {
        title: t('sections.systemInfo.name'),
        description: t('sections.systemInfo.description'),
        fields: ["createdAt", "updatedAt", "createdBy"],
      },
    ],

    fetchData: async (id: string) => {
      try {
        const rule: AiRule = await AiRulesAPI.Detail(id).request()

        if (!rule) throw new Error(t('messages.notFound'))

        return {
          id: rule.id,
          name: rule.name,
          description: rule.description || "",
          conditions: rule.conditions?.join(", ") || "",
          actions: rule.actions?.join(", ") || "",
          tags: rule.tags?.join(", ") || "",
          isActive: rule.isActive || false,
          createdAt: rule.createdAt
            ? new Date(rule.createdAt).toLocaleString("id-ID")
            : "",
          updatedAt: rule.updatedAt
            ? new Date(rule.updatedAt).toLocaleString("id-ID")
            : "",
          createdBy: rule.createdBy || "System",
        }
      } catch (error: any) {
        console.error("Error fetching AI Rule:", error)
        throw new Error(error?.message || t('messages.fetchError'))
      }
    },

    saveData: async (data: Record<string, any>) => {
      try {
        const payload: Partial<AiRule> = {
          name: data.name,
          description: data.description,
          conditions: data.conditions
            ? data.conditions
              .split(",")
              .map((c: string) => c.trim())
              .filter(Boolean)
            : [],
          actions: data.actions
            ? data.actions
              .split(",")
              .map((a: string) => a.trim())
              .filter(Boolean)
            : [],
          tags: data.tags
            ? data.tags
              .split(",")
              .map((t: string) => t.trim())
              .filter(Boolean)
            : [],
          isActive: data.isActive ?? true,
        }

        await AiRulesAPI.Update(data.id, payload).request()
      } catch (error: any) {
        console.error("Error updating AI Rule:", error)

        let message = t('messages.updateError')
        if (error?.response?.data?.messages?.length > 0) {
          message = error.response.data.messages[0]
        } else if (error?.message) {
          message = error.message
        }

        throw new Error(message)
      }
    },

    backRoute: "/ai-rules",
    successRoute: "/ai-rules",

    submitButtonText: t('buttons.update'),
    cancelButtonText: t('buttons.cancel'),
    showImagePreview: false,
  }

  return <DataEditorPage config={aiRuleEditorConfig} id={id} />
}
