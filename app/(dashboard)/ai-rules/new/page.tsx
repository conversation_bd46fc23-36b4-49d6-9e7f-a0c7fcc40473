"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { AiRulesAPI } from "@/lib/services/aiRulesApi"
import { AiRule } from "@/lib/repositories/aiRules/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

export default function NewAiRulePage() {
  const { t } = useLocalization("ai-rules", locales)

  const aiRuleEditorConfig: DataEditorConfig = {
    title: "New AI Rule",
    subtitle: "Create a new AI rule for automation or alerts",

    fields: [
      {
        name: "name",
        label: t('fields.name.label'),
        type: "text",
        placeholder: t('fields.name.placeholder'),
        description: `${t('fields.name.description')}\n\n${t('fields.name.examples')}`,
        validation: {
          required: true,
          minLength: 2,
          maxLength: 100,
        },
        group: "basic",
      },
      {
        name: "description",
        label: t('fields.description.label'),
        type: "textarea",
        placeholder: t('fields.description.placeholder'),
        description: `${t('fields.description.description')}\n\n${t('fields.description.examples')}`,
        rows: 4,
        validation: {
          maxLength: 500,
        },
        group: "basic",
      },
      {
        name: "conditions",
        label: t('fields.conditions.label'),
        type: "textarea",
        placeholder: t('fields.conditions.placeholder'),
        description: `${t('fields.conditions.description')}\n\n${t('fields.conditions.examples')}`,
        validation: {
          required: true,
        },
        group: "logic",
      },
      {
        name: "actions",
        label: t('fields.actions.label'),
        type: "textarea",
        placeholder: t('fields.actions.placeholder'),
        description: `${t('fields.actions.description')}\n\n${t('fields.actions.examples')}`,
        validation: {
          required: true,
        },
        group: "logic",
      },
      {
        name: "tags",
        label: t('fields.tags.label'),
        type: "text",
        placeholder: t('fields.tags.placeholder'),
        description: `${t('fields.tags.description')}\n\n${t('fields.tags.examples')}`,
        group: "meta",
      },
    ],

    sections: [
      {
        title: t('sections.basicInfo.name'),
        description: t('sections.basicInfo.description'),
        fields: ["name", "description"],
      },
      {
        title: t('sections.ruleLogic.name'),
        description: t('sections.ruleLogic.description'),
        fields: ["conditions", "actions"],
      },
      {
        title: t('sections.metadata.name'),
        description: t('sections.metadata.description'),
        fields: ["tags", "isActive"],
      },
    ],

    saveData: async (data: Record<string, any>, _isEdit: boolean) => {
      try {
        const payload: Partial<AiRule> = {
          name: data.name,
          description: data.description,
          conditions: data.conditions
            ? data.conditions
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
            : [],
          actions: data.actions
            ? data.actions
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
            : [],
          tags: data.tags
            ? data.tags
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
            : [],
          isActive: !!data.isActive,
        }

        await AiRulesAPI.Create(payload).request()
      } catch (error: any) {
        console.error("Error creating AI rule:", error)

        let errorMessage = "Failed to create AI rule"
        if (error?.response?.data?.messages?.length > 0) {
          errorMessage = error.response.data.messages[0]
        } else if (error?.message) {
          errorMessage = error.message
        }

        throw new Error(errorMessage)
      }
    },

    backRoute: "/ai-rules",
    successRoute: "/ai-rules",

    submitButtonText: "Create Rule",
    cancelButtonText: "Cancel",
    showImagePreview: false,
  }

  return <DataEditorPage config={aiRuleEditorConfig} />
}
