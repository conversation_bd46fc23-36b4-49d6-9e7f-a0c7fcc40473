{"title": "Edit Aturan AI", "subtitle": "<PERSON>bah kondisi aturan, tin<PERSON>an, dan metadata", "sections": {"basicInfo": {"name": "<PERSON><PERSON>", "description": "<PERSON>a dan ring<PERSON>an aturan"}, "ruleLogic": {"name": "<PERSON><PERSON><PERSON>", "description": "Tentukan kondisi dan tindakan"}, "metadata": {"name": "<PERSON><PERSON><PERSON>", "description": "Tag dan status"}, "systemInfo": {"name": "Info Sistem", "description": "Informasi pelacakan"}}, "fields": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama deskriptif untuk aturan ini", "description": "<PERSON>a yang jelas dan deskriptif yang mengidentif<PERSON>si tujuan aturan ini", "examples": "Contoh: <PERSON><PERSON><PERSON> Ke<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Jelaskan apa yang dilakukan aturan ini dan kapan harus dipicu...", "description": "Penjelasan detail tentang tujuan aturan, per<PERSON><PERSON>, dan hasil yang di<PERSON>pkan", "examples": "Contoh:\n• Aturan ini mengirim pesan tindak lanjut ketika pelanggan meninggalkan keranjang\n• Secara otomatis menyapa pelanggan baru dan menawarkan bantuan\n• Memberikan informasi harga ketika pelanggan bertanya tentang biaya"}, "conditions": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> frasa pemicu dipisahkan dengan koma", "description": "<PERSON><PERSON> k<PERSON>, frasa, atau pola yang akan memicu aturan ini. Gunakan koma untuk memisahkan beberapa kondisi.", "examples": "Contoh:\n• halo, hai, selamat pagi, salam\n• harga, biaya, berapa, price\n• bantuan, tolong, help, support\n• keluhan, masalah, problem, complaint"}, "actions": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tindakan yang harus diambil AI, dip<PERSON><PERSON><PERSON> dengan koma", "description": "Apa yang harus dilakukan AI ketika kondisi terpenuhi. Jelaskan perilaku respons atau tindakan spesifik.", "examples": "Contoh:\n• sapa dengan hangat, ta<PERSON><PERSON>, tanya bagaimana membantu\n• berikan informasi harga, sebutkan promosi saat ini\n• eskalasi ke agen manusia, kumpulkan detail kontak\n• minta maaf dengan tulus, ta<PERSON><PERSON> so<PERSON>, tindak lanjut"}, "tags": {"label": "Tag", "placeholder": "Tambahkan tag untuk organisasi (opsional)", "description": "Label opsional untuk membantu mengkategorikan dan mengorganisir aturan", "examples": "Contoh: layanan-p<PERSON><PERSON><PERSON>, pen<PERSON><PERSON>, duku<PERSON><PERSON>, pema<PERSON><PERSON>, otom<PERSON>, mendesak"}, "isActive": {"label": "Aktif", "description": "Aktifkan atau nonaktifkan aturan ini"}, "createdAt": {"label": "Tanggal Dibuat"}, "updatedAt": {"label": "<PERSON><PERSON><PERSON>"}, "createdBy": {"label": "Dibuat Oleh"}}, "buttons": {"update": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>"}, "validation": {"nameRequired": "<PERSON><PERSON> at<PERSON>n wajib diisi", "nameMinLength": "<PERSON><PERSON> minimal 2 karakter", "nameMaxLength": "<PERSON>a aturan tidak boleh lebih dari 100 karakter", "descriptionMaxLength": "Deskripsi tidak boleh lebih dari 500 karakter", "conditionsRequired": "<PERSON>mal satu kondisi diperlukan", "actionsRequired": "<PERSON><PERSON> satu tindakan diperlukan"}, "messages": {"updateSuccess": "Aturan AI ber<PERSON><PERSON>", "updateError": "<PERSON><PERSON>", "fetchError": "Gagal mengambil Aturan AI", "notFound": "Aturan AI tidak ditemukan"}}