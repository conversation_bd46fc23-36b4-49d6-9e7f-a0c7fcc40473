{"title": "Edit AI Rule", "subtitle": "Modify rule conditions, actions, and metadata", "sections": {"basicInfo": {"name": "Basic Info", "description": "Name and summary of the rule"}, "ruleLogic": {"name": "Rule Logic", "description": "Define the conditions and actions"}, "metadata": {"name": "<PERSON><PERSON><PERSON>", "description": "Tags and status"}, "systemInfo": {"name": "System Info", "description": "Tracking information"}}, "fields": {"name": {"label": "Rule Name", "placeholder": "Enter a descriptive name for this rule", "description": "A clear, descriptive name that identifies the purpose of this rule", "examples": "Examples: Abandoned Car<PERSON>minder, Welcome New Customer, Price Inquiry Response"}, "description": {"label": "Description", "placeholder": "Describe what this rule does and when it should be triggered...", "description": "Detailed explanation of the rule's purpose, behavior, and expected outcomes", "examples": "Examples:\n• This rule sends a follow-up message when customers abandon their cart\n• Automatically greets new customers and offers assistance\n• Provides pricing information when customers ask about costs"}, "conditions": {"label": "Conditions", "placeholder": "Enter trigger phrases separated by commas", "description": "Keywords, phrases, or patterns that will trigger this rule. Use commas to separate multiple conditions.", "examples": "Examples:\n• hello, hi, good morning, greetings\n• price, cost, how much, berapa harga\n• help, support, assistance, bantuan\n• complaint, problem, issue, keluhan"}, "actions": {"label": "Actions", "placeholder": "Enter actions the AI should take, separated by commas", "description": "What the AI should do when conditions are met. Describe the response behavior or specific actions.", "examples": "Examples:\n• greet warmly, offer assistance, ask how to help\n• provide pricing information, mention current promotions\n• escalate to human agent, collect contact details\n• apologize sincerely, offer solution, follow up"}, "tags": {"label": "Tags", "placeholder": "Add tags for organization (optional)", "description": "Optional labels to help categorize and organize rules", "examples": "Examples: customer-service, sales, support, marketing, automation, urgent"}, "isActive": {"label": "Active", "description": "Enable or disable this rule"}, "createdAt": {"label": "Created Date"}, "updatedAt": {"label": "Last Updated"}, "createdBy": {"label": "Created By"}}, "buttons": {"update": "Update Rule", "cancel": "Cancel"}, "validation": {"nameRequired": "Rule name is required", "nameMinLength": "Rule name must be at least 2 characters", "nameMaxLength": "Rule name cannot exceed 100 characters", "descriptionMaxLength": "Description cannot exceed 500 characters", "conditionsRequired": "At least one condition is required", "actionsRequired": "At least one action is required"}, "messages": {"updateSuccess": "AI Rule updated successfully", "updateError": "Failed to update AI Rule", "fetchError": "Failed to fetch AI Rule", "notFound": "AI Rule not found"}}