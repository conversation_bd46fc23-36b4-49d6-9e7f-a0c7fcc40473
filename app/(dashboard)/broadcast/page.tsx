"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Plus, Search, List, LayoutGrid } from "lucide-react"
import { BroadcastItem } from "@/components/broadcast/BroadcastItem"
import { BroadcastGridItem } from "@/components/broadcast/BroadcastGridItem"
import { BroadcastAPI } from "@/lib/services"
import { Broadcast } from "@/lib/repositories/broadcast/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { InformationalBanner } from "@/components/informational-banner"



export default function BroadcastPage() {
  const { t } = useLocalization("broadcast", locales)
  const router = useRouter()
  const [broadcasts, setBroadcasts] = useState<Broadcast[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')

  // Load view mode preference from localStorage
  useEffect(() => {
    const savedViewMode = localStorage.getItem('broadcast-view-mode') as 'list' | 'grid'
    if (savedViewMode && ['list', 'grid'].includes(savedViewMode)) {
      setViewMode(savedViewMode)
    }
  }, [])

  // Save view mode preference to localStorage
  const handleViewModeChange = (mode: 'list' | 'grid') => {
    setViewMode(mode)
    localStorage.setItem('broadcast-view-mode', mode)
  }

  const fetchBroadcasts = async (page = 1, search = "") => {
    try {
      setLoading(true)
      const response = await BroadcastAPI.All({
        page,
        per_page: 10,
        search: search || undefined,
        sort: [{ field: "createdAt", direction: "DESC" }],
      }).request()

      setBroadcasts(response.items)
      setTotalPages(1)
      setTotalItems(response.total)
      setCurrentPage(page)
    } catch (error) {
      console.error(t("page.errors.fetch_failed"), error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBroadcasts()
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchBroadcasts(1, searchTerm)
  }

  const handleDelete = async (id: string) => {
    if (!confirm(t("page.confirmations.delete"))) return

    try {
      await BroadcastAPI.Delete(id).request()
      fetchBroadcasts(currentPage, searchTerm)
    } catch (error) {
      console.error(t("page.errors.delete_failed"), error)
    }
  }

  const handleStartBroadcast = async (id: string) => {
    try {
      await BroadcastAPI.Start(id).request()
      fetchBroadcasts(currentPage, searchTerm)
    } catch (error) {
      console.error(t("page.errors.start_failed"), error)
    }
  }

  const handleCancelBroadcast = async (id: string) => {
    if (!confirm(t("page.confirmations.cancel"))) return

    try {
      await BroadcastAPI.Cancel(id).request()
      fetchBroadcasts(currentPage, searchTerm)
    } catch (error) {
      console.error(t("page.errors.cancel_failed"), error)
    }
  }



  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Informational Banner */}
      <InformationalBanner type="broadcasts" />

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{t("page.title")}</h1>
          <p className="text-gray-600">
            {t("page.subtitle")}
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* View Toggle */}
          <TooltipProvider>
            <div className="flex items-center border rounded-lg p-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleViewModeChange('list')}
                    className="h-8 px-3"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t("page.view_modes.list")}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleViewModeChange('grid')}
                    className="h-8 px-3"
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t("page.view_modes.grid")}</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </TooltipProvider>

          <Button
            onClick={() => router.push("/broadcast/new")}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {t("page.new_broadcast")}
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>{t("page.search.title")}</CardTitle>
          <CardDescription>
            {t("page.search.description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-2">
            <Input
              placeholder={t("page.search.placeholder")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" variant="outline">
              <Search className="h-4 w-4" />
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Broadcasts Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t("page.table.title", { count: totalItems })}</CardTitle>
          <CardDescription>{t("page.table.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : broadcasts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">{t("page.states.no_broadcasts")}</p>
              <Button
                onClick={() => router.push("/broadcast/new")}
                className="mt-4"
                variant="outline"
              >
                {t("page.states.create_first")}
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Broadcasts Display */}
              {viewMode === 'list' ? (
                <div className="space-y-4">
                  {broadcasts.map((broadcast) => (
                    <BroadcastItem
                      key={broadcast.id}
                      broadcast={broadcast}
                      onEdit={(id) => router.push(`/broadcast/${id}/edit`)}
                      onDelete={handleDelete}
                      onStart={handleStartBroadcast}
                      onCancel={handleCancelBroadcast}
                      onView={(id) => router.push(`/broadcast/${id}`)}
                    />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {broadcasts.map((broadcast) => (
                    <BroadcastGridItem
                      key={broadcast.id}
                      broadcast={broadcast}
                      onEdit={(id) => router.push(`/broadcast/${id}/edit`)}
                      onDelete={handleDelete}
                      onStart={handleStartBroadcast}
                      onCancel={handleCancelBroadcast}
                      onView={(id) => router.push(`/broadcast/${id}`)}
                    />
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => fetchBroadcasts(currentPage - 1, searchTerm)}
                    disabled={currentPage === 1}
                  >
                    {t("page.pagination.previous")}
                  </Button>
                  <span className="flex items-center px-4">
                    {t("page.pagination.page_info", { current: currentPage, total: totalPages })}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => fetchBroadcasts(currentPage + 1, searchTerm)}
                    disabled={currentPage === totalPages}
                  >
                    {t("page.pagination.next")}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
