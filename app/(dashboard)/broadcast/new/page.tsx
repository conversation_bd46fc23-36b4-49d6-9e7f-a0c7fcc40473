"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { BroadcastAPI } from "@/lib/services"
import TabbedBroadcastForm from "@/components/broadcast/TabbedBroadcastForm"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

export default function NewBroadcastPage() {
  const { t } = useLocalization("broadcast", locales)
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSave = async (data: {
    title: string
    message: string
    deviceId: string
    recipientTags?: string[]
    excludedRecipientIds?: string[]
    recipientSelectionType: "manual" | "tags"
  }) => {
    setIsSubmitting(true)
    try {
      const broadcast = await BroadcastAPI.Create({
        title: data.title,
        message: data.message,
        deviceId: data.deviceId,
        recipientTags: data.recipientTags,
        excludedRecipientIds: data.excludedRecipientIds,
        recipientSelectionType: data.recipientSelectionType,
      }).request()

      router.push(`/broadcast/${broadcast.id}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <TabbedBroadcastForm
      onSave={handleSave}
      onCancel={handleCancel}
      isSubmitting={isSubmitting}
      submitButtonText={t("form.new.submit")}
      title={t("form.new.title")}
      description={t("form.new.description")}
    />
  )
}
