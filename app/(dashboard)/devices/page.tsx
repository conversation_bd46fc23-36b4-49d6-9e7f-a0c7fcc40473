"use client"

import { useEffect, useState, useCallback, useRef } from "react"
import SessionsList from "@/components/layout/devices/SessionsList"
import { DevicesAPI, SyncResult } from "@/lib/services/devicesApi"
import { Device } from "@/lib/repositories/devices/interface"
import { devicesLocales } from "./locales"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "@/components/layout/locales"
import { Button } from "@/components/ui/button"
import { RefreshCw, QrCode } from "lucide-react"
import { InformationalBanner } from "@/components/informational-banner"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import Image from "next/image"
import { toast } from "sonner"
import { useAccountInfoContext } from "@/components/contexts/useAccountContext"

const k_TIMER_QR_SESSION = 120000;

export default function LinkedDevicesPage() {
  const { t } = useLocalization("devices", devicesLocales)
  const { t: tLayout } = useLocalization("layout", locales)
  const { account } = useAccountInfoContext()

  // Device state management
  const [devices, setDevices] = useState<Device[]>([])
  const [loading, setLoading] = useState(false)
  const [syncResult, setSyncResult] = useState<SyncResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  // QR state management
  const [qrDialogOpen, setQrDialogOpen] = useState(false)
  const [qrCode, setQrCode] = useState<string | null>(null)
  const [timeLeft, setTimeLeft] = useState<number>(0)
  const [qrDeviceId, setQrDeviceId] = useState<string | null>(null)
  const [isNewDevice, setIsNewDevice] = useState(false)

  // Refs for intervals
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Clean up intervals
  const cleanupIntervals = useCallback(() => {
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current)
      timerIntervalRef.current = null
    }
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current)
      syncIntervalRef.current = null
    }
  }, [])

  const fetchDevices = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await DevicesAPI.All({
        page: 1,
        per_page: 100,
        sort: [{ field: "createdAt", direction: "DESC" }],
      }).request()

      setDevices(response.items || [])
      setSyncResult(response.syncResult)
    } catch (err) {
      console.error("Error fetching devices:", err)
      setError("Failed to fetch devices")
    } finally {
      setLoading(false)
    }
  }, [])

  const syncAllSessions = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await DevicesAPI.SyncAll().request()
      setSyncResult(result)
      // Refresh devices after sync
      await fetchDevices()
    } catch (err) {
      console.error("Error syncing all sessions:", err)
      setError("Failed to sync all sessions")
    } finally {
      setLoading(false)
    }
  }, [fetchDevices])

  const deleteDevice = useCallback(async (deviceId: string) => {
    try {
      await DevicesAPI.Delete(deviceId).request()
      // Refresh devices after delete
      await fetchDevices()
    } catch (err) {
      console.error("Error deleting device:", err)
      setError("Failed to delete device")
    }
  }, [fetchDevices])

  const updateDevice = useCallback(async (deviceId: string, data: any) => {
    try {
      await DevicesAPI.Update(deviceId, data).request()
      // Refresh devices after update
      await fetchDevices()
    } catch (err) {
      console.error("Error updating device:", err)
      setError("Failed to update device")
    }
  }, [fetchDevices])

  // QR Management Functions
  const getQRCode = useCallback(async (deviceId: string) => {
    try {
      const response = await DevicesAPI.GetQR(deviceId).request()
      if (response.qr) {
        setQrCode(response.qr)
        setTimeLeft(k_TIMER_QR_SESSION / 1000)
        return true
      }
      return false
    } catch (err: any) {
      toast.error("Failed to get QR code")
      return false
    }
  }, [])

  const startTimer = useCallback(() => {
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current)
    }

    timerIntervalRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          // Timer expired - refetch QR code
          if (qrDeviceId) {
            getQRCode(qrDeviceId)
          }
          return k_TIMER_QR_SESSION / 1000
        }
        return prev - 1
      })
    }, 1000)
  }, [qrDeviceId, getQRCode])

  const startSyncInterval = useCallback(() => {
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current)
    }

    // Sync all sessions every 10 seconds during QR display
    syncIntervalRef.current = setInterval(() => {
      syncAllSessions()
    }, 10000)
  }, [])

  const showQRForDevice = useCallback(async (deviceId: string, isNew: boolean = false) => {
    setQrDeviceId(deviceId)
    setIsNewDevice(isNew)

    const success = await getQRCode(deviceId)
    if (success) {
      setQrDialogOpen(true)
      startTimer()
      startSyncInterval()
    }
  }, [getQRCode, startTimer, startSyncInterval])

  const startNewDeviceSession = useCallback(async () => {
    cleanupIntervals()
    setQrCode(null)
    setQrDeviceId(null)
    setTimeLeft(0)

    try {
      const response = await DevicesAPI.Link({ name: account.name + "'s Device #" + (devices.length + 1) }).request()

      if (response.sessionId) {
        // If device is immediately returned, add it to the list
        if (response.device) {
          setDevices((prev) => [response.device, ...prev])
        }

        // Show QR for the new device
        await showQRForDevice(response.device.id, true)
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to start session")
    }
  }, [account.name, cleanupIntervals, showQRForDevice])

  const closeQRDialog = useCallback(() => {
    cleanupIntervals()
    setQrDialogOpen(false)
    setQrCode(null)
    setQrDeviceId(null)
    setTimeLeft(0)
    setIsNewDevice(false)
  }, [cleanupIntervals])

  const toggleDeviceState = useCallback(async (deviceId: string, isActive: boolean) => {
    try {
      await DevicesAPI.ToggleState(deviceId, isActive).request()
      // Refresh devices after toggle
      await fetchDevices()
    } catch (err) {
      console.error("Error toggling device state:", err)
      setError("Failed to toggle device state")
    }
  }, [fetchDevices])

  useEffect(() => {
    fetchDevices()
  }, [fetchDevices])

  // Cleanup intervals on unmount
  useEffect(() => {
    return () => {
      cleanupIntervals()
    }
  }, [cleanupIntervals])

  return (
    <div className="space-y-4">
      <InformationalBanner type="devices" />
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">{t("page_title")}</h2>

        {/* Action buttons */}
        <div className="flex gap-2 items-center">
          <Button
            onClick={syncAllSessions}
            disabled={loading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {t("actions.sync_all_sessions")}
          </Button>
          <Button onClick={startNewDeviceSession}>
            <QrCode className="h-4 w-4" />
            {tLayout("add_devices.link_device")}
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <SessionsList
        devices={devices?.map((device) => ({
          ...device,
          id: device.id,
          name: device.me?.pushName || device.name,
          platform: device.platform || "phone",
        }))}
        loading={loading}
        onDelete={deleteDevice}
        onUpdate={updateDevice}
        onShowQR={showQRForDevice}
        onToggleState={toggleDeviceState}
      />

      {/* Centralized QR Dialog */}
      <Dialog open={qrDialogOpen} onOpenChange={closeQRDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isNewDevice ? tLayout("add_devices.link_new_device") : tLayout("add_devices.reconnect_device")}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              {tLayout("add_devices.instructions", {
                settings: tLayout("add_devices.settings_text"),
                linked_devices: tLayout("add_devices.linked_devices_text"),
              })}
            </p>
            <div className="flex justify-center flex-col items-center gap-2">
              <div className="p-6 border rounded bg-muted/50">
                {!qrCode ? (
                  <QrCode className="w-32 h-32 text-muted-foreground" />
                ) : (
                  <Image src={qrCode} alt="qr-code" width={240} height={240} />
                )}
              </div>
              {qrCode && (
                <p className="text-xs text-gray-300 text-center">
                  {tLayout("add_devices.available_time", { time: timeLeft })}
                </p>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
