"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { DatasourcesAPI } from "@/lib/services"
import { Datasource } from "@/lib/repositories/datasources/interface"
import DatasourceForm from "@/components/datasource/DatasourceForm"
import DatasourceView from "@/components/datasource/DatasourceView"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

interface DatasourcePageClientProps {
  datasourceId: string
}

export default function DatasourcePageClient({ datasourceId }: DatasourcePageClientProps) {
  const { t } = useLocalization("datasources", locales)
  const router = useRouter()
  const [datasource, setDatasource] = useState<Datasource | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const fetchDatasource = async () => {
    try {
      setLoading(true)
      const response = await DatasourcesAPI.Detail(datasourceId).request()
      setDatasource(response)
    } catch (error) {
      console.error(t("errors.fetch_failed"), error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (datasourceId) {
      fetchDatasource()
    }
  }, [datasourceId])

  const handleSave = async (data: {
    name: string
    type: string
    content?: string
    url?: string
    accessKey?: string
    isActive: boolean
  }) => {
    setIsSubmitting(true)
    try {
      await DatasourcesAPI.Update(datasourceId, {
        name: data.name,
        type: data.type,
        content: data.content,
        url: data.url,
        accessKey: data.accessKey,
        isActive: data.isActive,
      }).request()

      setIsEditing(false)
      fetchDatasource() // Refresh data
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm(t("detail_page.delete_confirmation"))) {
      return
    }

    try {
      await DatasourcesAPI.Delete(datasourceId).request()
      router.push("/datasources")
    } catch (error) {
      console.error(t("errors.delete_failed"), error)
      alert(t("detail_page.delete_error"))
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    )
  }

  if (!datasource) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-gray-500">{t("detail_page.not_found")}</p>
          <Button onClick={() => router.push("/datasources")} className="mt-4">
            {t("detail_page.back_to_list")}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      {isEditing ? (
        <DatasourceForm
          initialDatasource={datasource}
          onSave={handleSave}
          onCancel={() => setIsEditing(false)}
          isSubmitting={isSubmitting}
          submitButtonText={t("detail_page.edit.submit")}
          title={t("detail_page.edit.title")}
          description={t("detail_page.edit.description")}
        />
      ) : (
        <DatasourceView
          datasource={datasource}
          onEdit={() => setIsEditing(true)}
          onDelete={handleDelete}
          onBack={() => router.back()}
        />
      )}
    </>
  )
}
