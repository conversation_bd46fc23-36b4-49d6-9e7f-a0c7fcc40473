"use client"

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { useLocalization } from "@/localization/functions/client"
import {
  AlertCircle,
  BookOpen,
  Bug,
  Clock,
  CreditCard,
  Lightbulb,
  Mail,
  MessageCircle,
  Phone,
  Search,
  Settings,
  Star
} from "lucide-react"
import * as React from "react"
import { supportLocales } from "./locales"

const iconMap = {
  Mail,
  MessageCircle,
  Phone,
  BookOpen,
  Bug,
  Lightbulb,
  CreditCard,
  Settings,
}

export default function SupportPage() {
  const { t, locale } = useLocalization("support", supportLocales)
  const [searchTerm, setSearchTerm] = React.useState("")
  const [selectedCategory, setSelectedCategory] = React.useState("all")
  const [supportConfig, setSupportConfig] = React.useState<any>(null)

  // Load the appropriate config file based on current language
  React.useEffect(() => {
    const loadSupportConfig = async () => {
      try {
        // Default to English if language is not supported
        const language = locale === "id" ? "id" : "en"
        const config = await import(`./config/support-config-${language}.json`)
        setSupportConfig(config.default)
      } catch (error) {
        console.error("Failed to load support config:", error)
        // Fallback to English config
        const config = await import(`./config/support-config-en.json`)
        setSupportConfig(config.default)
      }
    }

    loadSupportConfig()
  }, [locale])

  // Don't render until config is loaded
  if (!supportConfig) {
    return (
      <div className="container mx-auto py-8 space-y-8 p-2 h-full overflow-y-auto">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading support information...</p>
        </div>
      </div>
    )
  }

  const filteredFAQ = supportConfig.faq.filter((item: any) => {
    const matchesSearch =
      item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory =
      selectedCategory === "all" ||
      item.category.toLowerCase().replace(" ", "_") === selectedCategory

    return matchesSearch && matchesCategory
  })

  const handleContactAction = (action: any) => {
    switch (action.type) {
      case "email":
        const subject = action.subject ? `?subject=${encodeURIComponent(action.subject)}` : ""
        window.open(`mailto:${action.target}${subject}`)
        break
      case "phone":
        window.open(`tel:${action.target}`)
        break
      case "link":
        window.open(action.target, "_blank")
        break
    }
  }

  return (
    <div className="container mx-auto py-8 space-y-8 p-2 h-full overflow-y-auto">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">{t("page.title")}</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {t("page.description")}
        </p>
      </div>

      {/* Emergency Support Alert */}
      {supportConfig.emergencyContact.available && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">{t("emergency.title")}</AlertTitle>
          <AlertDescription className="text-red-700">
            {t("emergency.description")}
            <Button
              variant="link"
              className="p-0 h-auto ml-2 text-red-600"
              onClick={() => handleContactAction({
                type: "link",
                target: supportConfig.emergencyContact.contact.whatsapp
              })}
            >
              {t("emergency.contact_button")}
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Support Channels */}
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">{t("channels.title")}</CardTitle>
          <CardDescription>{t("channels.subtitle")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {supportConfig.supportChannels.map((channel: any) => {
              const IconComponent = iconMap[channel.icon as keyof typeof iconMap]
              const colorClasses = {
                blue: "border-blue-200 bg-blue-50 hover:bg-blue-100",
                green: "border-green-200 bg-green-50 hover:bg-green-100",
                purple: "border-purple-200 bg-purple-50 hover:bg-purple-100",
                orange: "border-orange-200 bg-orange-50 hover:bg-orange-100",
              }

              return (
                <Card
                  key={channel.id}
                  className={cn(
                    "relative cursor-pointer transition-colors",
                    colorClasses[channel.color as keyof typeof colorClasses]
                  )}
                  onClick={() => handleContactAction(channel.action)}
                >
                  {channel.recommended && (
                    <Badge className="absolute -top-2 -right-2 bg-yellow-500">
                      <Star className="h-3 w-3 mr-1" />
                      {t("channels.recommended_badge")}
                    </Badge>
                  )}
                  <CardContent className="p-6 text-center space-y-3">
                    <div className={`mx-auto w-12 h-12 rounded-full flex items-center justify-center bg-${channel.color}-100`}>
                      <IconComponent className={`h-6 w-6 text-${channel.color}-600`} />
                    </div>
                    <div>
                      <h3 className="font-semibold">{channel.name}</h3>
                      <p className="text-sm text-muted-foreground">{channel.description}</p>
                    </div>
                    <div className="space-y-1 text-xs">
                      <div className="flex items-center justify-center gap-1">
                        <Clock className="h-3 w-3" />
                        {t("channels.response_time", { time: channel.responseTime })}
                      </div>
                      <div className="text-muted-foreground">
                        {t("channels.availability", { hours: channel.availability })}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>{t("quick_actions.title")}</CardTitle>
          <CardDescription>{t("quick_actions.subtitle")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {supportConfig.quickActions.map((action: any) => {
              const IconComponent = iconMap[action.icon as keyof typeof iconMap]

              return (
                <Card
                  key={action.id}
                  className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => handleContactAction(action.action)}
                >
                  <CardContent className="p-4 text-center space-y-2">
                    <IconComponent className="h-8 w-8 mx-auto text-primary" />
                    <h3 className="font-medium">{action.title}</h3>
                    <p className="text-sm text-muted-foreground">{action.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>{t("faq.title")}</CardTitle>
          <CardDescription>{t("faq.subtitle")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t("faq.search_placeholder")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full sm:w-auto">
              <TabsList className="grid grid-cols-3 lg:grid-cols-6">
                <TabsTrigger value="all">{t("faq.categories.all")}</TabsTrigger>
                <TabsTrigger value="getting_started">{t("faq.categories.getting_started")}</TabsTrigger>
                <TabsTrigger value="billing">{t("faq.categories.billing")}</TabsTrigger>
                <TabsTrigger value="integrations">{t("faq.categories.integrations")}</TabsTrigger>
                <TabsTrigger value="security">{t("faq.categories.security")}</TabsTrigger>
                <TabsTrigger value="technical">{t("faq.categories.technical")}</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* FAQ List */}
          {filteredFAQ.length > 0 ? (
            <Accordion type="single" collapsible className="w-full">
              {filteredFAQ.map((faq: any) => (
                <AccordionItem key={faq.id} value={faq.id}>
                  <AccordionTrigger className="text-left">
                    <div className="flex items-center gap-2">
                      <span>{faq.question}</span>
                      <Badge variant="outline" className="text-xs">
                        {faq.category}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{t("faq.no_results")}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Business Hours and Tips */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Business Hours */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {t("business_hours.title")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-sm space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">
                  {t("business_hours.weekdays", {
                    start: supportConfig.businessHours.weekdays.start,
                    end: supportConfig.businessHours.weekdays.end
                  })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">
                  {t("business_hours.weekends", {
                    start: supportConfig.businessHours.weekends.start,
                    end: supportConfig.businessHours.weekends.end
                  })}
                </span>
              </div>
              <Separator />
              <div className="text-xs text-muted-foreground">
                {t("business_hours.timezone", { timezone: supportConfig.businessHours.timezone })}
              </div>
              <div className="text-xs text-green-600 font-medium">
                {t("business_hours.note")}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Support Tips */}
        <Card>
          <CardHeader>
            <CardTitle>{t("tips.title")}</CardTitle>
            <CardDescription>{t("tips.subtitle")}</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {supportConfig.tips.map((tip: string, index: number) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                  <span>{tip}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Contact Information Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <h3 className="text-lg font-semibold">Still need help?</h3>
            <p className="text-muted-foreground">
              Our support team is here to help you succeed with CS AI
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Button
                onClick={() => handleContactAction({
                  type: "email",
                  target: supportConfig.contact.email
                })}
                className="gap-2"
              >
                <Mail className="h-4 w-4" />
                {t("actions.send_email")}
              </Button>
              <Button
                variant="outline"
                onClick={() => handleContactAction({
                  type: "link",
                  target: supportConfig.contact.whatsappLink
                })}
                className="gap-2"
              >
                <MessageCircle className="h-4 w-4" />
                {t("actions.chat_whatsapp")}
              </Button>
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>{supportConfig.contact.email}</div>
              <div>{supportConfig.contact.whatsapp}</div>
              <div>{supportConfig.contact.address}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
