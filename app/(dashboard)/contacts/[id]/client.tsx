"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { ContactsAPI } from "@/lib/services/contactsApi"
import { Contact } from "@/lib/repositories/contacts/interface"
import { useLocalization } from "@/localization/functions/client"
import { contactsLocales } from "../locales"

interface ClientPageProps {
  id: string
}

export default function ClientPage({ id }: ClientPageProps) {
  const { t } = useLocalization("contacts", contactsLocales)

  let myNotes: { text: string; createdAt: string }[] = []
  // Contact editor configuration for editing existing contacts
  const contactEditorConfig: DataEditorConfig = {
    title: "Edit Contact",
    subtitle: "Edit customer contact information",

    fields: [
      // Basic Information
      {
        name: "name",
        label: t('fields.name.label'),
        type: "text",
        placeholder: t('fields.name.placeholder'),
        description: `${t('fields.name.description')}\n\n${t('fields.name.examples')}`,
        validation: {
          required: true,
          minLength: 2,
          maxLength: 100,
        },
        group: "basic",
      },
      {
        name: "phone",
        label: t('fields.phone.label'),
        type: "phone",
        placeholder: t('fields.phone.placeholder'),
        description: `${t('fields.phone.description')}\n\n${t('fields.phone.examples')}`,
        validation: {
          required: true,
          pattern: /^[\+]?[1-9][\d]{0,15}$/,
        },
        group: "basic",
      },
      {
        name: "email",
        label: t('fields.email.label'),
        type: "email",
        placeholder: t('fields.email.placeholder'),
        description: `${t('fields.email.description')}\n\n${t('fields.email.examples')}`,
        validation: {
          pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        },
        group: "basic",
      },

      // Classification
      {
        name: "tags",
        label: t('fields.tags.label'),
        type: "text",
        placeholder: t('fields.tags.placeholder'),
        description: `${t('fields.tags.description')}\n\n${t('fields.tags.examples')}`,
        group: "classification",
      },

      // Additional Information
      {
        name: "company",
        label: t('fields.company.label'),
        type: "text",
        placeholder: t('fields.company.placeholder'),
        description: `${t('fields.company.description')}\n\n${t('fields.company.examples')}`,
        validation: {
          maxLength: 100,
        },
        group: "additional",
      },
      {
        name: "position",
        label: t('fields.position.label'),
        type: "text",
        placeholder: t('fields.position.placeholder'),
        description: `${t('fields.position.description')}\n\n${t('fields.position.examples')}`,
        validation: {
          maxLength: 100,
        },
        group: "additional",
      },
      {
        name: "address",
        label: t('fields.address.label'),
        type: "textarea",
        placeholder: t('fields.address.placeholder'),
        description: `${t('fields.address.description')}\n\n${t('fields.address.examples')}`,
        rows: 3,
        validation: {
          maxLength: 500,
        },
        group: "additional",
      },
      {
        name: "birthday",
        label: t('fields.birthday.label'),
        type: "date",
        placeholder: t('fields.birthday.placeholder'),
        description: `${t('fields.birthday.description')}\n\n${t('fields.birthday.examples')}`,
        group: "additional",
      },

      // Notes
      {
        name: "notes",
        label: t('fields.notes.label'),
        type: "textarea",
        placeholder: t('fields.notes.placeholder'),
        description: `${t('fields.notes.description')}\n\n${t('fields.notes.examples')}`,
        rows: 4,
        validation: {
          maxLength: 1000,
        },
        group: "notes",
      },

      // System Information (read-only)
      {
        name: "createdAt",
        label: t('fields.createdAt.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
      {
        name: "updatedAt",
        label: t('fields.updatedAt.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
      {
        name: "createdBy",
        label: t('fields.createdBy.label'),
        type: "text",
        disabled: true,
        group: "system",
      },
    ],

    sections: [
      {
        title: t('sections.basicInformation.title'),
        description: t('sections.basicInformation.description'),
        fields: ["name", "phone", "email"],
      },
      {
        title: t('sections.classification.title'),
        description: t('sections.classification.description'),
        fields: ["tags"],
      },
      {
        title: t('sections.additionalInformation.title'),
        description: t('sections.additionalInformation.description'),
        fields: ["company", "position", "address", "birthday"],
      },
      {
        title: t('sections.notes.title'),
        description: t('sections.notes.description'),
        fields: ["notes"],
      },
      {
        title: t('sections.systemInformation.title'),
        description: t('sections.systemInformation.description'),
        fields: ["createdAt", "updatedAt", "createdBy"],
      },
    ],

    // Data operations
    fetchData: async (id: string) => {
      try {
        const contactData = await ContactsAPI.Detail(id).request()

        if (!contactData) {
          throw new Error("Contact not found")
        }

        // Transform data for the form
        // Note: Some fields (company, position, address, birthday) are not in the Contact interface
        // but are used in the form. We'll handle them gracefully.
        const extendedContactData = contactData as Contact & {
          company?: string
          position?: string
          address?: string
          birthday?: string
        }

        myNotes = contactData.notes as [{ text: string; createdAt: string }]
        const transformedData = {
          id: contactData.id,
          name: contactData.name || "",
          phone: contactData.phone || "",
          email: contactData.email || "",
          tags: contactData.tags ? contactData.tags.join(", ") : "",
          company: extendedContactData.company || "",
          position: extendedContactData.position || "",
          address: extendedContactData.address || "",
          birthday: extendedContactData.birthday
            ? new Date(extendedContactData.birthday).toISOString().split("T")[0]
            : "",
          notes: contactData.notes
            ? contactData.notes.map((a) => a.text).join(", ")
            : "",
          createdAt: contactData.createdAt
            ? new Date(contactData.createdAt).toLocaleString("id-ID")
            : "",
          updatedAt: contactData.updatedAt
            ? new Date(contactData.updatedAt).toLocaleString("id-ID")
            : "",
          createdBy: contactData.createdBy || "System",
        }

        return transformedData
      } catch (error: any) {
        console.error("Error fetching contact:", error)

        if (error?.response?.status === 404) {
          throw new Error("Contact not found")
        }

        throw new Error(error?.message || "Failed to fetch contact data")
      }
    },

    saveData: async (data: Record<string, any>, _isEdit: boolean) => {
      try {
        // Transform tags from comma-separated string to array
        // Note: Some fields (company, position, address, birthday) are not in the Contact interface
        // but we'll include them in the payload for the API to handle
        const transformedData = {
          name: data.name,
          phone: data.phone,
          email: data.email || undefined,
          tags: data.tags
            ? data.tags
              .split(",")
              .map((tag: string) => tag.trim())
              .filter(Boolean)
            : [],
          // Handle notes - preserve existing notes and add new one if changed
          notes: data.notes
            ? data.notes
              .split(",")
              .map((tag: string) => tag.trim())
              .filter(Boolean)
              .map((note: string) => {
                const existingNote = myNotes.find((a) => a.text === note)
                return {
                  text: note,
                  createdAt:
                    existingNote?.createdAt || new Date().toISOString(),
                }
              })
            : [],
          // Extended fields (not in Contact interface but used in form)
          ...(data.company && { company: data.company }),
          ...(data.position && { position: data.position }),
          ...(data.address && { address: data.address }),
          ...(data.birthday && { birthday: data.birthday }),
        }

        await ContactsAPI.Update(data.id, transformedData).request()
      } catch (error: any) {
        console.error("Error updating contact:", error)

        // Extract error message from the response
        let errorMessage = "Failed to update contact"
        if (
          error?.response?.data?.messages &&
          error.response.data.messages.length > 0
        ) {
          errorMessage = error.response.data.messages[0]
        } else if (error?.message) {
          errorMessage = error.message
        }

        if (error?.response?.status === 404) {
          throw new Error("Contact not found")
        }

        throw new Error(errorMessage)
      }
    },

    // Navigation
    backRoute: "/contacts",
    successRoute: "/contacts",

    // Customization
    submitButtonText: t('buttons.update'),
    cancelButtonText: t('buttons.cancel'),
    showImagePreview: false,
    maxFileSize: 5, // 5MB
    allowedFileTypes: ["image/jpeg", "image/png", "image/gif"],
  }

  return <DataEditorPage config={contactEditorConfig} id={id} />
}
