"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { ContactsAPI } from "@/lib/services/contactsApi"
import { useLocalization } from "@/localization/functions/client"
import { contactsLocales } from "../locales"

export default function NewContactPage() {
  const { t } = useLocalization("contacts", contactsLocales)

  // Contact editor configuration for creating new contacts
  const contactEditorConfig: DataEditorConfig = {
    title: "New Contact",
    subtitle: "Create new customer contact",

    fields: [
      // Basic Information
      {
        name: "name",
        label: t('fields.name.label'),
        type: "text",
        placeholder: t('fields.name.placeholder'),
        description: `${t('fields.name.description')}\n\n${t('fields.name.examples')}`,
        validation: {
          required: true,
          minLength: 2,
          maxLength: 100,
        },
        group: "basic",
      },
      {
        name: "phone",
        label: t('fields.phone.label'),
        type: "phone",
        placeholder: t('fields.phone.placeholder'),
        description: `${t('fields.phone.description')}\n\n${t('fields.phone.examples')}`,
        validation: {
          required: true,
          pattern: /^[\+]?[1-9][\d]{0,15}$/,
        },
        group: "basic",
      },
      {
        name: "email",
        label: t('fields.email.label'),
        type: "email",
        placeholder: t('fields.email.placeholder'),
        description: `${t('fields.email.description')}\n\n${t('fields.email.examples')}`,
        validation: {
          pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        },
        group: "basic",
      },

      // Classification
      {
        name: "tags",
        label: t('fields.tags.label'),
        type: "text",
        placeholder: t('fields.tags.placeholder'),
        description: `${t('fields.tags.description')}\n\n${t('fields.tags.examples')}`,
        group: "classification",
      },

      // Additional Information
      {
        name: "company",
        label: t('fields.company.label'),
        type: "text",
        placeholder: t('fields.company.placeholder'),
        description: `${t('fields.company.description')}\n\n${t('fields.company.examples')}`,
        validation: {
          maxLength: 100,
        },
        group: "additional",
      },
      {
        name: "position",
        label: t('fields.position.label'),
        type: "text",
        placeholder: t('fields.position.placeholder'),
        description: `${t('fields.position.description')}\n\n${t('fields.position.examples')}`,
        validation: {
          maxLength: 100,
        },
        group: "additional",
      },
      {
        name: "address",
        label: t('fields.address.label'),
        type: "textarea",
        placeholder: t('fields.address.placeholder'),
        description: `${t('fields.address.description')}\n\n${t('fields.address.examples')}`,
        rows: 3,
        validation: {
          maxLength: 500,
        },
        group: "additional",
      },
      {
        name: "birthday",
        label: t('fields.birthday.label'),
        type: "date",
        placeholder: t('fields.birthday.placeholder'),
        description: `${t('fields.birthday.description')}\n\n${t('fields.birthday.examples')}`,
        group: "additional",
      },

      // Notes
      {
        name: "notes",
        label: t('fields.notes.label'),
        type: "textarea",
        placeholder: t('fields.notes.placeholder'),
        description: `${t('fields.notes.description')}\n\n${t('fields.notes.examples')}`,
        rows: 4,
        validation: {
          maxLength: 1000,
        },
        group: "notes",
      },
    ],

    sections: [
      {
        title: t('sections.basicInformation.title'),
        description: t('sections.basicInformation.description'),
        fields: ["name", "phone", "email"],
      },
      {
        title: t('sections.classification.title'),
        description: t('sections.classification.description'),
        fields: ["tags"],
      },
      {
        title: t('sections.additionalInformation.title'),
        description: t('sections.additionalInformation.description'),
        fields: ["company", "position", "address", "birthday"],
      },
      {
        title: t('sections.notes.title'),
        description: t('sections.notes.description'),
        fields: ["notes"],
      },
    ],

    // Data operations
    saveData: async (data: Record<string, any>, _isEdit: boolean) => {
      try {
        // Transform tags from comma-separated string to array
        // Note: Some fields (company, position, address, birthday) are not in the Contact interface
        // but we'll include them in the payload for the API to handle
        const transformedData = {
          name: data.name,
          phone: data.phone,
          email: data.email || undefined,
          tags: data.tags
            ? data.tags
              .split(",")
              .map((tag: string) => tag.trim())
              .filter(Boolean)
            : [],
          notes: data.notes
            ? data.notes
              .split(",")
              .map((tag: string) => tag.trim())
              .filter(Boolean)
              .map((note: string) => {
                return {
                  text: note,
                  createdAt: new Date().toISOString(),
                }
              })
            : [],
          // Extended fields (not in Contact interface but used in form)
          ...(data.company && { company: data.company }),
          ...(data.position && { position: data.position }),
          ...(data.address && { address: data.address }),
          ...(data.birthday && { birthday: data.birthday }),
        }

        await ContactsAPI.Create(transformedData).request()
      } catch (error: any) {
        console.error(t('errors.createError'), error)

        // Extract error message from the response
        let errorMessage = t('errors.createFailed')
        if (
          error?.response?.data?.messages &&
          error.response.data.messages.length > 0
        ) {
          errorMessage = error.response.data.messages[0]
        } else if (error?.message) {
          errorMessage = error.message
        }

        throw new Error(errorMessage)
      }
    },

    // Navigation
    backRoute: "/contacts",
    successRoute: "/contacts",

    // Customization
    submitButtonText: t('buttons.create'),
    cancelButtonText: t('buttons.cancel'),
    showImagePreview: false,
    maxFileSize: 5, // 5MB
    allowedFileTypes: ["image/jpeg", "image/png", "image/gif"],
  }

  return <DataEditorPage config={contactEditorConfig} />
}
