{"page_title": "Customer Contacts", "page_subtitle": "Manage and monitor customer contacts for CS operations", "headers": {"name": "Name", "phone": "Phone", "email": "Email", "tags": "Tags", "notes_count": "Notes Count", "created_date": "Created Date", "updated_date": "Updated Date", "created_by": "Created By"}, "sort_options": {"name": "Name", "phone": "Phone", "email": "Email", "created_at": "Created Date", "updated_at": "Updated Date"}, "date_filter_options": {"today": "Today", "yesterday": "Yesterday", "this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "all": "All Time"}, "filters": {"has_phone": "Has Phone", "has_email": "<PERSON>", "has_tags": "Has Tags"}, "stats": {"title": "Contact Statistics", "total_contacts": "Total Contacts", "active_contacts": "Active Contacts", "contacts_with_email": "With Email", "contacts_with_tags": "With Tags", "added_recently": "added recently", "all_contacts_description": "All contacts in the system", "percentage_of_total": "% of total", "contact_status": "Contact Status", "top_tags": "Top Tags", "created_by": "Created By", "all_time": "all time", "status_options": {"active": "Active", "deleted": "Deleted", "pending": "Pending", "archived": "Archived"}}, "routes": {"add": "/contacts/new", "edit": "/contacts/{{id}}", "bulk": "/contacts/bulk"}, "labels": {"tag": "Tag", "created_by": "Created By", "contact": "Contact", "interactions": "Interactions"}, "fields": {"name": {"label": "Contact Name", "placeholder": "Enter the full name of the contact", "description": "The complete name of the person or organization you're communicating with", "examples": "Examples: <PERSON>, <PERSON>, ABC Corporation, PT Maju Jaya"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number with country code", "description": "Primary phone number for WhatsApp communication. Include country code for international numbers.", "examples": "Examples:\n• ******-123-4567 (US format)\n• +62-812-3456-7890 (Indonesia format)\n• +44-20-7946-0958 (UK format)\n• +86-138-0013-8000 (China format)"}, "email": {"label": "Email Address", "placeholder": "Enter email address", "description": "Primary email address for contact communication and identification", "examples": "Examples: <EMAIL>, <EMAIL>, <EMAIL>"}, "company": {"label": "Company", "placeholder": "Enter company name", "description": "Organization or company where the contact works", "examples": "Examples: Microsoft Corporation, PT Telkom Indonesia, ABC Trading Ltd"}, "position": {"label": "Position", "placeholder": "Enter job title or position", "description": "Job title or role of the contact within their organization", "examples": "Examples: Sales Manager, CEO, Marketing Director, Customer Service Representative"}, "address": {"label": "Address", "placeholder": "Enter complete address", "description": "Physical address or location information for the contact", "examples": "Examples:\n• 123 Main Street, New York, NY 10001, USA\n• Jl. Sudirman No. 45, Jakarta Pusat, Indonesia\n• 10 Downing Street, London SW1A 2AA, UK\n• Building 2, Floor 5, Business District"}, "birthday": {"label": "Birthday", "placeholder": "Select birthday date", "description": "Date of birth for personal contacts (optional)", "examples": "Examples: 1990-05-15, 1985-12-25, 1992-03-08"}, "notes": {"label": "Notes", "placeholder": "Add any additional notes about this contact...", "description": "Internal notes, preferences, or important information about the contact", "examples": "Examples:\n• Prefers communication in the morning\n• VIP customer - priority support\n• Interested in premium products\n• Previous order: #12345 on 2024-01-15\n• Speaks English and Indonesian"}, "tags": {"label": "Tags", "placeholder": "Add tags for organization (optional)", "description": "Labels to help categorize and organize contacts for better management", "examples": "Examples: customer, prospect, vip, supplier, partner, lead, inactive, priority"}, "createdAt": {"label": "Created Date"}, "updatedAt": {"label": "Last Updated"}, "createdBy": {"label": "Created By"}}, "buttons": {"update": "Update Contact", "create": "Create Contact", "cancel": "Cancel"}, "sections": {"basicInformation": {"title": "Basic Information", "description": "Essential contact details"}, "classification": {"title": "Classification", "description": "Tags and categories"}, "additionalInformation": {"title": "Additional Information", "description": "Optional details"}, "notes": {"title": "Notes", "description": "Additional information and remarks"}, "systemInformation": {"title": "System Information", "description": "Tracking and audit information"}}, "errors": {"createFailed": "Failed to create contact", "updateFailed": "Failed to update contact", "createError": "Error creating contact", "bulkImportError": "Bulk import error", "importFailed": "Import failed", "fetchForUpdateFailed": "Failed to fetch contacts for bulk update", "fetchForDeleteFailed": "Failed to fetch contacts"}, "bulk": {"title": "Contacts", "subtitle": "Bulk import, update, and delete contact records", "headers": {"import": ["Name", "Phone", "Email", "Tags"], "delete": ["Name", "Phone", "Email", "Company", "Created Date"]}, "itemName": "contact", "itemNamePlural": "contacts"}}