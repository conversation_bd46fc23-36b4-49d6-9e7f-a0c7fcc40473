{"page_title": "Kontak Pelanggan", "page_subtitle": "Ke<PERSON>la dan pantau kontak pelanggan untuk operasi CS", "headers": {"name": "<PERSON><PERSON>", "phone": "Telepon", "email": "Email", "tags": "Tag", "notes_count": "<PERSON><PERSON><PERSON>", "created_date": "Tanggal Dibuat", "updated_date": "<PERSON><PERSON>", "created_by": "Dibuat Oleh"}, "sort_options": {"name": "<PERSON><PERSON>", "phone": "Telepon", "email": "Email", "created_at": "Tanggal Dibuat", "updated_at": "<PERSON><PERSON>"}, "date_filter_options": {"today": "<PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "this_week": "<PERSON><PERSON>", "last_week": "<PERSON><PERSON>", "this_month": "<PERSON><PERSON><PERSON>", "last_month": "<PERSON><PERSON><PERSON>", "all": "Sepanjang W<PERSON>"}, "filters": {"has_phone": "Memiliki Telepon", "has_email": "Me<PERSON><PERSON><PERSON>", "has_tags": "Memiliki Tag"}, "stats": {"title": "Statistik Kontak", "total_contacts": "Total Kontak", "active_contacts": "Kontak Aktif", "contacts_with_email": "<PERSON><PERSON>", "contacts_with_tags": "Dengan Tag", "added_recently": "ditambahkan baru-baru ini", "all_contacts_description": "<PERSON><PERSON><PERSON> kontak dalam sistem", "percentage_of_total": "% dari total", "contact_status": "Status Kontak", "top_tags": "Tag Teratas", "created_by": "Dibuat Oleh", "all_time": "sepanjang waktu", "status_options": {"active": "Aktif", "deleted": "<PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "archived": "Diarsip<PERSON>"}}, "routes": {"add": "/contacts/new", "edit": "/contacts/{{id}}", "bulk": "/contacts/bulk"}, "labels": {"tag": "Tag", "created_by": "Dibuat Oleh", "contact": "Kontak", "interactions": "Interaksi"}, "fields": {"name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama lengkap kontak", "description": "<PERSON>a lengkap orang atau organisasi yang berkomunikasi dengan <PERSON>a", "examples": "Contoh: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CV <PERSON>"}, "phone": {"label": "Nomor Telepon", "placeholder": "Ma<PERSON>kkan nomor telepon dengan kode negara", "description": "Nomor telepon utama untuk komunikasi WhatsApp. Sertakan kode negara untuk nomor internasional.", "examples": "Contoh:\n• +62-812-3456-7890 (format Indonesia)\n• ******-123-4567 (format AS)\n• +44-20-7946-0958 (format Inggris)\n• +86-138-0013-8000 (format China)"}, "email": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> email", "description": "Alamat email utama untuk komunikasi dan identifikasi kontak", "examples": "Contoh: <EMAIL>, <EMAIL>, <EMAIL>"}, "company": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama <PERSON>an", "description": "Organisasi atau perusahaan tempat kontak bekerja", "examples": "Contoh: Microsoft Corporation, PT Telkom Indonesia, ABC Trading Ltd"}, "position": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Masukkan jabatan atau posisi", "description": "Jabatan atau peran kontak dalam organisasi mereka", "examples": "Contoh: <PERSON><PERSON><PERSON>, CEO, <PERSON><PERSON><PERSON>, Customer Service Representative"}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> alamat le<PERSON>p", "description": "<PERSON><PERSON><PERSON> atau informasi lokasi untuk kontak", "examples": "Contoh:\n• Jl<PERSON> No. 45, Jakarta Pusat, Indonesia\n• Jl. <PERSON> 123, Yogyakarta 55271\n• Gedung ABC Lt. 5, <PERSON><PERSON><PERSON>, Jakarta\n• Komplek Perumahan Indah Blok A No. 10"}, "birthday": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> tanggal lahir", "description": "Tanggal lahir untuk kontak personal (opsional)", "examples": "Contoh: 1990-05-15, 1985-12-25, 1992-03-08"}, "notes": {"label": "Catatan", "placeholder": "Tambahkan catatan tambahan tentang kontak ini...", "description": "Catatan internal, preferensi, atau informasi penting tentang kontak", "examples": "Contoh:\n• Lebih suka komunikasi di pagi hari\n• Pelanggan VIP - dukungan prioritas\n• Tertarik dengan produk premium\n• Pesanan sebelumnya: #12345 pada 15-01-2024\n• Berbicara bahasa Indonesia dan Inggris"}, "tags": {"label": "Tag", "placeholder": "Tambahkan tag untuk organisasi (opsional)", "description": "Label untuk membantu mengkategorikan dan mengorganisir kontak untuk manajemen yang lebih baik", "examples": "Contoh: pelanggan, prospek, vip, supplier, mitra, lead, tidak-aktif, prioritas"}, "createdAt": {"label": "Tanggal Dibuat"}, "updatedAt": {"label": "<PERSON><PERSON><PERSON>"}, "createdBy": {"label": "Dibuat Oleh"}}, "buttons": {"update": "<PERSON><PERSON><PERSON>", "create": "Buat Kontak", "cancel": "<PERSON><PERSON>"}, "sections": {"basicInformation": {"title": "Informasi <PERSON>", "description": "Detail kontak yang penting"}, "classification": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Tag dan kategori"}, "additionalInformation": {"title": "Informasi <PERSON>", "description": "Detail opsional"}, "notes": {"title": "Catatan", "description": "Informasi dan keterangan tambahan"}, "systemInformation": {"title": "Informasi Sistem", "description": "Informasi pelacakan dan audit"}}, "errors": {"createFailed": "<PERSON><PERSON> membuat kontak", "updateFailed": "<PERSON><PERSON> kontak", "createError": "<PERSON><PERSON>r membuat kontak", "bulkImportError": "Error impor massal", "importFailed": "<PERSON><PERSON><PERSON> gagal", "fetchForUpdateFailed": "Gagal mengambil kontak untuk pembaruan massal", "fetchForDeleteFailed": "Gagal mengambil kontak"}, "bulk": {"title": "Kontak", "subtitle": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, dan hapus rekord kontak secara massal", "headers": {"import": ["<PERSON><PERSON>", "Telepon", "Email", "Tag"], "delete": ["<PERSON><PERSON>", "Telepon", "Email", "<PERSON><PERSON><PERSON><PERSON>", "Tanggal Dibuat"]}, "itemName": "kontak", "itemNamePlural": "kontak"}}