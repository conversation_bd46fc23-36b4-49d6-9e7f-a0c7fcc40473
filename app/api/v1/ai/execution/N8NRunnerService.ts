import { AIInferenceEngine } from "./AIInferenceEngine"
import { ERROR_CODES } from "@/app/api/error_codes"

const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL!
const N8N_CALLBACK_AI_EXECUTION = process.env.N8N_CALLBACK_AI_EXECUTION!!
const N8N_CALLBACK_AI_WORKFLOW_EXECUTION =
  process.env.N8N_CALLBACK_AI_WORKFLOW_EXECUTION!!

export class N8nRunnerService implements AIInferenceEngine {
  async execute(
    conversationId: string,
    executionId: string,
    context: string,
    payload: any,
  ): Promise<{
    status: "success" | "failed"
    data?: any
    errors?: string[]
    errorCodes?: string[]
  }> {
    try {
      const res = await fetch(`${N8N_WEBHOOK_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Webhook-AI-Execution": N8N_CALLBACK_AI_EXECUTION,
          "X-Webhook-AI-Workflow-Execution-Step":
            N8N_CALLBACK_AI_WORKFLOW_EXECUTION,
          "X-Conversation-SessionId": conversationId,
          "X-AI-Workflow-Execution-ID": executionId,
        },
        body: JSON.stringify({ context, ...payload }),
      })

      if (!res.ok) {
        const text = await res.text()
        return {
          status: "failed",
          errors: [`n8n webhook error: ${res.status} - ${text}`],
          errorCodes: [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
        }
      }

      const data = await res.json()
      return {
        status: "success",
        data,
      }
    } catch (error: any) {
      return {
        status: "failed",
        errors: [error.message || "Unknown error calling n8n webhook"],
        errorCodes: [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
      }
    }
  }
}
