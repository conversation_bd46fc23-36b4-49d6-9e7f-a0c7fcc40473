import { N8nRunnerService } from "./N8NRunnerService"
import { AiBusinessLogic } from "./AIBusinessLogic"
import {
  aiRulesBusinessLogic,
  messageTemplatesBusinessLogic,
} from "@/lib/repositories/businessLogics"
import { AiExecutionDatasourceRepository } from "./mongodb"
import { conversationMessagesBusinessLogic } from "@/lib/repositories/businessLogics"
import { knowledgeBaseBusinessLogic } from "@/lib/repositories/businessLogics"
import { datasourcesBusinessLogic } from "@/lib/repositories/businessLogics"

export const n8nRunner = new N8nRunnerService()
const dataSourceRepo = new AiExecutionDatasourceRepository(
  conversationMessagesBusinessLogic,
)
export const aiBusinessLogic = new AiBusinessLogic(
  n8nRunner,
  aiRulesBusinessLogic,
  messageTemplatesBusinessLogic,
  dataSourceRepo,
  datasourcesBusinessLogic,
  knowledgeBaseBusinessLogic,
)
