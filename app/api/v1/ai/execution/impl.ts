import { SessionContext } from "@/lib/repositories/auth/types"
import { conversationBusinessLogic } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { implHandleSendMessage } from "../../functions/messages/send/impl"
import { getExecutionIdAiExtras } from "../../functions/webhook/impl"
import { handleSendMessageAITestingConversation } from "../../test-conversations/[id]/ai-reply/impl"
import { AiBusinessLogic } from "./AIBusinessLogic"

export async function implHandleTryAnswer(
  conversationId: string,
  executionId: string,
  body: { message: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  const rules = await businessLogic.tryAnswer(
    conversationId,
    executionId,
    body.message,
    context,
  )
  return {
    status: 200,
    body: new ResponseWrapper("success", {
      message: "Executed TRY_ANSWER",
      rules,
    }),
  }
}

export async function implHandleReply(
  sessionId: string,
  executionId: string,
  body: { final_message: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  try {
    const cs_ai_extras = await getExecutionIdAiExtras(executionId)
    if (!cs_ai_extras) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation context not found"],
          ["CONVERSATION_CONTEXT_NOT_FOUND"],
        ),
      }
    }

    await implHandleSendMessage(
      { conversationId: cs_ai_extras.msg_room_id, text: body.final_message },
      context,
    )
    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Executed REPLY",
        // context,
        // result
      }),
    }
  } catch (error) {
    console.error("❌ Error during AI workflow handling:", error);
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        ["INTERNAL_SERVER_ERROR"],
      ),
    }
  }
}
