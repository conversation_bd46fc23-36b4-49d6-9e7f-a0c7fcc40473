// export interface IAiRepository {
//   getRulesForMessage(message: string): Promise<Array<{ id: string; content: string }>>;
//   getMessageTemplateForRule(ruleId: string): Promise<Array<{ id: string; content: string }>>;
//   getMessageTemplateById(templateId: string): Promise<{ id: string; content: string, dataSource: string, variables: string[], fields: string[] }>;
//   getRAG(message: string): Promise<Array<any>>;
//   getTemplateForMessage(message: string): Promise<Array<{ id: string; content: string }>>;
// }

import { SessionContext } from "@/lib/repositories/auth/types";

export interface IDatasourceRepository {
  getDataWithDataSourceAndVariables(
    dataSource: string,
    variables: string[],
    fieldsToRetrieve: string[],
    context: SessionContext,
  ): Promise<Array<Record<string, string>>>
  getReplyContext(messageId: string, context: SessionContext,
  ): Promise<{ replyContext: string }>
  getLastMessages(
    sessionId: string,
    limit: number,
    context: SessionContext,
  ): Promise<Array<{ from: string; content: string }>>
}
