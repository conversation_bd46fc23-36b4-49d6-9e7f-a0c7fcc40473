import { NextRequest, NextResponse } from "next/server"
import { conversationBusinessLogic } from "@/lib/repositories/businessLogics"
import {
  implHandleGetAllConversations,
  implHandleCreateConversation,
} from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "../../sharedFunction"
import { parseSearchParams } from "../../parseSearchParams"
import { conversationsSearchConfig } from "../search-configs/entities/conversations"

export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { searchParams } = new URL(req.url)

    const { params, response: parsedResponse } = parseSearchParams(searchParams, conversationsSearchConfig)
    if (parsedResponse) {
      return parsedResponse
    }
    const result = await implHandleGetAllConversations(
      conversationBusinessLogic,
      context,
      params,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Conversation GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleCreateConversation(
      body,
      conversationBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Conversation POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
