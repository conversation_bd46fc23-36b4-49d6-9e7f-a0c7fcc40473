import { NextRequest, NextResponse } from "next/server"
import { conversationMessagesBusinessLogic } from "@/lib/repositories/businessLogics"
import {
  implHandleUpdateConversationMessage,
  implHandleDeleteConversationMessage,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function PUT(
  req: NextRequest,
  routeContext: { params: Promise<{ messageId: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { messageId } = await routeContext.params
    const body = await req.json()
    const result = await implHandleUpdateConversationMessage(
      messageId,
      body,
      conversationMessagesBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("conversationMessage PUT route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function DELETE(
  req: NextRequest,
  routeContext: { params: Promise<{ messageId: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { messageId } = await routeContext.params
    const result = await implHandleDeleteConversationMessage(
      messageId,
      conversationMessagesBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("conversationMessage DELETE route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
