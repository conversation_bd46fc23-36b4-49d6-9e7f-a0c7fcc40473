import { NextRequest, NextResponse } from "next/server"
import { conversationBusinessLogic, conversationMessagesBusinessLogic } from "@/lib/repositories/businessLogics"
import {
  implHandleGetAllConversationMessages,
  implHandleCreateConversationMessage,
} from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { conversationMessagesSearchConfig } from "../../../search-configs/entities/conversationMessageSearchConfig"
import { parseSearchParams } from "@/app/api/parseSearchParams"

export async function GET(req: NextRequest, routeContext: { params: Promise<{ conversationId: string }> },) {
  try {

    const { conversationId } = await routeContext.params

    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const { searchParams } = new URL(req.url)

    const { params, response: parsedResponse } = parseSearchParams(searchParams, conversationMessagesSearchConfig)
    if (parsedResponse) {
      return parsedResponse
    }

    const result = await implHandleGetAllConversationMessages(
      conversationId,
      conversationMessagesBusinessLogic,
      context,
      params,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("conversationMessage GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest, routeContext: { params: Promise<{ conversationId: string }> },) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { conversationId } = await routeContext.params
    const body = await req.json()
    const result = await implHandleCreateConversationMessage(
      body,
      conversationMessagesBusinessLogic,
      conversationBusinessLogic,
      conversationId,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("conversationMessage POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
