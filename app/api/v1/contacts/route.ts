import { NextRequest, NextResponse } from "next/server"
import { contactsBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleGetAllContacts, implHandleCreateContact } from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { contactsSearchConfig } from "../search-configs/entities/contacts"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { buildSessionContext } from "../../sharedFunction"
import { contactAccessManager } from "@/lib/repositories/accessManagers"
import { parseSearchParams } from "../../parseSearchParams"

export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { searchParams } = new URL(req.url)

    const { params, response: parsedResponse } = parseSearchParams(searchParams, contactsSearchConfig)
    if (parsedResponse) {
      return parsedResponse
    }

    const result = await implHandleGetAllContacts(
      contactsBusinessLogic,
      params,
      context,
      contactAccessManager(context.organization?.id || context.user.id),
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Contacts GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleCreateContact(
      body,
      contactsBusinessLogic,
      context,
      contactAccessManager(context.organization?.id || context.user.id),
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Contacts POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
