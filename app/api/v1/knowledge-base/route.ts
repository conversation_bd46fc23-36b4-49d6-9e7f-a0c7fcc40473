import { NextRequest, NextResponse } from "next/server"
import { knowledgeBaseBusinessLogic } from "@/lib/repositories/businessLogics"
import {
  implHandleGetAllKnowledgeBase,
  implHandleCreateKnowledgeBase,
} from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// GET /api/v1/knowledge-base - Get all knowledge base entries
export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { searchParams } = new URL(req.url)

    // Parse query parameters
    const params = {
      search: searchParams.get("search") || undefined,
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      includeDeleted: searchParams.get("includeDeleted") === "true",
      category: searchParams.get("category") || undefined,
      tags: searchParams.get("tags")?.split(",").filter(Boolean) || undefined,
      isActive: searchParams.get("isActive")
        ? searchParams.get("isActive") === "true"
        : undefined,
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "DESC",
    }

    const result = await implHandleGetAllKnowledgeBase(
      knowledgeBaseBusinessLogic,
      context,
      params,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in GET /api/v1/knowledge-base:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

// POST /api/v1/knowledge-base - Create knowledge base entry or bulk operations
export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const { searchParams } = new URL(req.url)
    const operation = searchParams.get("operation")

    const result = await implHandleCreateKnowledgeBase(
      body,
      knowledgeBaseBusinessLogic,
      context,
    )

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in POST /api/v1/knowledge-base:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
