import { buildSessionContext } from "@/app/api/sharedFunction"
import { providers } from "@/lib/providers"
import { devicesBusinessLogic } from "@/lib/repositories/businessLogics"
import { NextRequest, NextResponse } from "next/server"

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const { id } = await params
    const device = await devicesBusinessLogic.getById(id, context)

    if (!device) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          error: "Device not found",
          errorCodes: ["NOT_FOUND"],
        },
        { status: 404 }
      )
    }

    const qr = await providers[process.env.WHATSAPP_PROVIDER!].getQr(device.sessionId)

    return NextResponse.json(
      {
        status: "success",
        data: { qr, session: device.sessionId },
        messages: [],
        errorCodes: [],
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Get device route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 }
    )
  }
}