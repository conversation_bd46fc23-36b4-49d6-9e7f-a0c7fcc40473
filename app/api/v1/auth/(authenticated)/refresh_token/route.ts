import { NextRequest, NextResponse } from "next/server"
import { implHandleRefreshToken } from "./impl"
import { authBusinessLogic } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function POST(req: NextRequest) {
  const tokenFromCookie = req.cookies.get("token")?.value
  const refreshTokenFromCookie = req.cookies.get("refresh_token")?.value

  // Only refresh_token is strictly required
  if (!refreshTokenFromCookie) {
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["refresh_token_must_not_empty"],
        ["INVALID_TOKEN"],
      ),
      { status: 401 },
    )
  }

  const result = await implHandleRefreshToken(
    {
      token: tokenFromCookie,
      refresh_token: refreshTokenFromCookie,
    },
    authBusinessLogic,
  )

  if (result.status !== 200) {
    const failResponse = NextResponse.json(result.body, { status: result.status })
    failResponse.cookies.set("token", "", {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
      path: "/",
      maxAge: 0,
    })
    failResponse.cookies.set("refresh_token", "", {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
      path: "/",
      maxAge: 0,
    })
    return failResponse
  }

  const accessToken = result.body.data?.token
  const refreshToken = result.body.data?.refresh_token

  const response = NextResponse.json(
    new ResponseWrapper("success", {
      success: true,
    }),
    { status: result.status },
  )

  if (accessToken) {
    response.cookies.set("token", accessToken, {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
      path: "/",
      maxAge: 15 * 60,
    })
  }

  if (refreshToken) {
    response.cookies.set("refresh_token", refreshToken, {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
      path: "/",
      maxAge: 30 * 24 * 60 * 60,
    })
  }

  return response
}
