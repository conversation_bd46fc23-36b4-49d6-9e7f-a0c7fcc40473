import { RealtimeConversationList, RealtimeConversationRoom } from "@/lib/realtime/model"
import { SessionContext } from "@/lib/repositories/auth/types"
import {
  contactsBusinessLogic,
  conversationBusinessLogic,
  conversationMessagesBusinessLogic,
  workflowExecutionsBusinessLogic,
} from "@/lib/repositories/businessLogics"
import { Contact } from "@/lib/repositories/contacts"
import { ConversationMessageCreateInput } from "@/lib/repositories/conversationMessages/interface"
import {
  Conversation,
  ConversationCreateInput,
} from "@/lib/repositories/conversations/interface"
import { driver } from "@/lib/repositories/LiveMongoDriver"
import { driver as redisDriver } from "@/lib/repositories/LiveRedisDriver"
import { NextRequest, NextResponse } from "next/server"
import { implHandleCreateAiWorkflowExecution } from "../../ai-workflow-executions/impl"
import { implHandleTryAnswer } from "../../ai/execution/impl"
import { aiBusinessLogic } from "../../ai/execution/shared"
import { enrichParticipantInfo } from "../../conversations/impl"
import { getContextFromSession } from "../../devices/link/route"
import { getGlobalAISetting } from "../../settings/global-ai/getGlobalAiSetting"
import { SessionToConversationRoomStore, sessionToConversationStore } from "./SessionToConversationRoomStore"
import { MessagePayload, WahaWebhookPayload } from "./wahaWebhookPayload"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

// Helper function to send realtime updates for conversation changes
async function sendConversationRealtimeUpdates(conversation: Conversation, context: SessionContext) {
  try {
    const enriched = await enrichParticipantInfo(context, [conversation])
    await RealtimeConversationList.UPDATE_CONVERSATION(enriched[0]).send()
    await RealtimeConversationRoom.UPDATE_CONVERSATION(enriched[0]).send()
  } catch (error) {
    console.error("Error sending realtime updates:", error)
  }
}

export async function processWebhook(req: NextRequest): Promise<ResponseWrapper<{
  conversationId: string
  messageId: string
} | {
  message: string
} | any>> {
  const body = (await req.json()) as WahaWebhookPayload

  if (body?.event !== "message" || !body.payload || body.payload.fromMe || !isPrivateMessage(body.payload)) {
    return new ResponseWrapper("success", { message: "Not a private message" })
  }

  const payload = body.payload
  const phone = extractPhone(payload.from)
  if (!phone) return invalidPhoneResponse(payload.from)

  const context = await getContextFromSession(body.session)
  if (!context) return sessionNotFoundResponse()

  const conversationMapping = await sessionToConversationStore.getByWahaConversationId(payload.from)

  const messagesTable = driver.getCollection("waha_message_webhook")
  await messagesTable.insertOne(body)

  let contact = await findOrCreateContact(phone, payload, context)
  let conversation: Conversation

  try {
    if (conversationMapping) {
      const existing = await conversationBusinessLogic.getById(conversationMapping.id, context) as Conversation | null

      if (!existing || existing.status !== "OPEN") {
        conversation = await createNewConversation(payload, contact, context, existing?.id, sessionToConversationStore, body)
      } else {
        conversation = await updateConversation(payload, existing, context)
      }
    } else {
      conversation = await createAndMapConversation(payload, contact, context, sessionToConversationStore, body)
    }

    const message = await createMessage(conversation.id, payload, contact.id, context)
    console.log(`✅ Stored WAHA message: ${message.id} in conversation: ${conversation.id}`)
    await RealtimeConversationRoom.NEW_MESSAGE(
      conversation.id,
      message
    ).send()

    if (conversation.isAiEnabled !== false) {
      await handleAiWorkflow(conversation.id, payload, context, body.session)
    }

    await RealtimeConversationRoom.UPDATE_MESSAGE(conversation.id).send()
    return new ResponseWrapper("success", {
      conversationId: conversation.id,
      messageId: message.id,
    })
  } catch (error) {
    console.error("❌ Failed to handle WAHA conversation/message:", error)
    return new ResponseWrapper("failed", null, ["❌ Failed to handle WAHA conversation/message"], ["INTERNAL_SERVER_ERROR"])
  }
}

// --- Utility & helper functions ---

function isPrivateMessage(payload: WahaWebhookPayload["payload"]) {
  return payload.from.includes("@c.us")
}

function extractPhone(raw: string) {
  return (raw || "").replace("@c.us", "") || null
}

function invalidPhoneResponse(from: string) {
  console.error("❌ Invalid customer phone number:", from)
  return new ResponseWrapper("failed", null, ["❌ Invalid customer phone number"], ["INVALID_PHONE_NUMBER"])
}


async function storeConversationDeviceSessionId(conversationId: string, session: string) {
  await redisDriver.set(`session_to_conversationroom-${conversationId}`, session)
}

export function getConversationDeviceSessionId(conversationId: string) {
  return redisDriver.get<string>(`session_to_conversationroom-${conversationId}`)
}

async function storeExecutionIdToContext(executionId: string, context: SessionContext) {
  await redisDriver.set(`workflow-executions-context-${executionId}`, context)
}

export async function getExecutionIdContext(executionId: string): Promise<SessionContext | null> {
  const context = await redisDriver.get<SessionContext>(`workflow-executions-context-${executionId}`)
  return context
}

function sessionNotFoundResponse() {
  return new ResponseWrapper("failed", null, ["❌ Session not found"], ["SESSION_NOT_FOUND"])
}

async function findOrCreateContact(phone: string, payload: MessagePayload, context: SessionContext): Promise<Contact> {
  let contact = await contactsBusinessLogic.getByPhone(phone, context)
  if (!contact) {
    contact = await contactsBusinessLogic.create({
      name: payload._data?.pushName || phone,
      phone,
    }, context)
  }
  return contact
}

async function updateConversation(payload: MessagePayload, existing: Conversation, context: SessionContext) {
  const updateData = {
    lastMessage: {
      body: payload.body,
      fromMe: false,
      _data: undefined,
      ack: payload.ack,
    },
    lastMessageAt: new Date(),
  }

  const updated = await conversationBusinessLogic.update(existing.id, updateData, context) as Conversation
  console.log(`✅ Updated existing OPEN conversation room: ${updated.id}`)
  const enriched = await enrichParticipantInfo(context, [updated])
  await RealtimeConversationList.UPDATE_CONVERSATION(enriched[0]).send()
  await RealtimeConversationRoom.UPDATE_CONVERSATION(enriched[0]).send()
  return updated
}

async function createNewConversation(
  payload: MessagePayload,
  contact: Contact,
  context: SessionContext,
  previousConversationId: string | undefined,
  sessionToConversationStore: SessionToConversationRoomStore,
  body: WahaWebhookPayload
): Promise<Conversation> {

  const isAiEnabled = await getGlobalAISetting(context.user.id)

  const input: ConversationCreateInput = {
    name: payload._data?.pushName || payload.from,
    participants: [contact.id, context.user.id],
    isAiEnabled: body.metadata?.INTERNAL_TESTING || isAiEnabled,
    tags: [],
    isActive: true,
    status: "OPEN",
    previousConversationId,
    isTesting: body.metadata?.INTERNAL_TESTING || false
  }

  const conversation = await conversationBusinessLogic.create(input, context)
  await storeConversationDeviceSessionId(conversation.id, body.session)

  await sessionToConversationStore.store({
    session: body.session,
    id: conversation.id,
    waha_conversation_id: payload.from,
  })

  console.log(`✅ Created new conversation room (linked to previous): ${conversation.id}`)
  await RealtimeConversationList.NEW_CONVERSATION(conversation).send()

  return conversation
}

async function createAndMapConversation(
  payload: MessagePayload,
  contact: Contact,
  context: SessionContext,
  sessionToConversationStore: SessionToConversationRoomStore,
  body: WahaWebhookPayload
): Promise<Conversation> {
  const input: ConversationCreateInput = {
    name: payload._data?.pushName || payload.from,
    participants: [contact.id, context.user.id],
    tags: [],
    isActive: true,
    status: "OPEN",
    isTesting: body.metadata?.INTERNAL_TESTING || false
  }

  const conversation = await conversationBusinessLogic.create(input, context)

  await sessionToConversationStore.storeNew({
    session: body.session,
    id: conversation.id,
    waha_conversation_id: payload.from,
  })

  console.log(`✅ Created new conversation room: ${conversation.id}`)
  await RealtimeConversationList.NEW_CONVERSATION(conversation).send()

  return conversation
}

async function createMessage(
  conversationId: string,
  payload: MessagePayload,
  senderId: string,
  context: SessionContext
) {
  const input: ConversationMessageCreateInput = {
    conversationId,
    content: payload.body || "",
    messageType: payload.hasMedia ? "IMAGE" : "TEXT",
    senderId,
    metadata: {
      wahaMessageId: payload.id,
      wahaTimestamp: payload.timestamp,
      //wahaSession: payload.session,
      wahaAck: payload.ack,
      wahaAckName: payload.ackName,
      wahaSource: payload.source,
      wahaData: payload._data,
      hasMedia: payload.hasMedia,
      media: payload.media,
      replyTo: payload.replyTo,
      fromMe: payload.fromMe,
      ack: payload.ack,
    },
  }

  return await conversationMessagesBusinessLogic.create(input, context)
}

export async function saveExecutionIdAiExtras(
  executionId: string,
  cs_ai_extras: {
    waha_session: string
    waha_conversation: string
    ai_workflow_execution_id: string
    msg_room_id: string
  }
) {
  await redisDriver.set(
    `workflow-executions-context-${executionId}-to-conversation`,
    cs_ai_extras
  );
}

export async function getExecutionIdAiExtras(executionId: string) {
  return redisDriver.get<{
    waha_session: string
    waha_conversation: string
    ai_workflow_execution_id: string
    msg_room_id: string
  }>(`workflow-executions-context-${executionId}-to-conversation`)
}

export async function handleAiWorkflow(
  conversationId: string,
  payload: {
    from: string
    body: string
  },
  context: SessionContext,
  deviceSessionId: string
): Promise<{ executionId: string } | null> {
  try {
    const updatedConversation = await conversationBusinessLogic.updateAiAnsweringState(conversationId, true, context);
    if (updatedConversation) {
      await sendConversationRealtimeUpdates(updatedConversation, context)
    }

    const executionResult = await implHandleCreateAiWorkflowExecution(
      {
        customerId: payload.from,
        originalMessage: payload.body,
      },
      workflowExecutionsBusinessLogic,
      context
    );

    const executionId = executionResult?.body?.data?.id;
    if (!executionId) {
      const errorConversation = await conversationBusinessLogic.updateAiAnsweringState(conversationId, false, context);
      if (errorConversation) {
        await sendConversationRealtimeUpdates(errorConversation, context)
      }
      return null;
    }

    await storeExecutionIdToContext(executionId, context);

    const cs_ai_extras = {
      waha_session: deviceSessionId,
      waha_conversation: payload.from,
      ai_workflow_execution_id: executionId,
      msg_room_id: conversationId,
    };
    await saveExecutionIdAiExtras(executionId, cs_ai_extras)

    await implHandleTryAnswer(
      conversationId,
      executionId,
      { message: payload.body },
      aiBusinessLogic,
      context
    );

    // Set AI answering state to false when done
    const completedConversation = await conversationBusinessLogic.updateAiAnsweringState(conversationId, false, context);
    if (completedConversation) {
      await sendConversationRealtimeUpdates(completedConversation, context)
    }

    return { executionId };

  } catch (error) {
    console.error("❌ Error during AI workflow handling:", error);
    const errorConversation = await conversationBusinessLogic.updateAiAnsweringState(conversationId, false, context);
    if (errorConversation) {
      await sendConversationRealtimeUpdates(errorConversation, context)
    }
    return null;
  }
}
