import { driver as redisDriver } from "@/lib/repositories/LiveRedisDriver"
import { driver as mongoDriver } from "@/lib/repositories/LiveMongoDriver"
import { Collection } from "mongodb"

type MappingEntry = {
  session: string
  id: string // conversationId
  waha_conversation_id: string
}

const REDIS_KEY_PREFIX = "session_to_conversationroom"
const REDIS_TTL = 60 * 60 * 12 // 12 hours

export class SessionToConversationRoomStore {
  private collection: Collection<MappingEntry>

  constructor() {
    this.collection = mongoDriver.getCollection<MappingEntry>("session_to_conversationroom")
    this.ensureIndexes()
  }

  private async ensureIndexes() {
    await this.collection.createIndexes([
      { key: { id: 1 }, unique: true },
      { key: { waha_conversation_id: 1 }, unique: true },
    ])
  }

  private redisKeyByConversationId(conversationId: string) {
    return `${REDIS_KEY_PREFIX}-conversation-${conversationId}`
  }

  private redisKeyByWahaConversationId(wahaConversationId: string) {
    return `${REDIS_KEY_PREFIX}-waha-${wahaConversationId}`
  }

  async store(mapping: MappingEntry): Promise<void> {
    await this.collection.updateOne(
      { waha_conversation_id: mapping.waha_conversation_id },
      { $set: mapping },
      { upsert: true }
    )

    // Store in Redis (both keys)
    await redisDriver.set(this.redisKeyByConversationId(mapping.id), mapping, REDIS_TTL)
    await redisDriver.set(this.redisKeyByWahaConversationId(mapping.waha_conversation_id), mapping, REDIS_TTL)
  }

  async storeNew(mapping: MappingEntry): Promise<void> {
    await this.collection.insertOne(mapping)

    // Store in Redis (both keys)
    await redisDriver.set(this.redisKeyByConversationId(mapping.id), mapping, REDIS_TTL)
    await redisDriver.set(this.redisKeyByWahaConversationId(mapping.waha_conversation_id), mapping, REDIS_TTL)
  }

  async getByConversationId(conversationId: string): Promise<MappingEntry | null> {
    const redisKey = this.redisKeyByConversationId(conversationId)
    let mapping = await redisDriver.get<MappingEntry>(redisKey)

    if (!mapping) {
      mapping = await this.collection.findOne({ id: conversationId })
      if (mapping) {
        await redisDriver.set(redisKey, mapping, REDIS_TTL)
        await redisDriver.set(this.redisKeyByWahaConversationId(mapping.waha_conversation_id), mapping, REDIS_TTL)
      }
    }

    return mapping
  }

  async getByWahaConversationId(wahaConversationId: string): Promise<MappingEntry | null> {
    const redisKey = this.redisKeyByWahaConversationId(wahaConversationId)
    let mapping = await redisDriver.get<MappingEntry>(redisKey)

    if (!mapping) {
      mapping = await this.collection.findOne({ waha_conversation_id: wahaConversationId })
      if (mapping) {
        await redisDriver.set(redisKey, mapping, REDIS_TTL)
        await redisDriver.set(this.redisKeyByConversationId(mapping.id), mapping, REDIS_TTL)
      }
    }

    return mapping
  }
}

const sessionToConversationStore = new SessionToConversationRoomStore()
export { sessionToConversationStore }
