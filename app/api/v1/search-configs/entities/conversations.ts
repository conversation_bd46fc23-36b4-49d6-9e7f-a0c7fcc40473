import MESSAGE_KEYS from "@/app/api/message_keys"
import { StringSort } from "../sort/StringSort"
import { BaseSearchConfig } from "./baseSearchConfig"
import { SearchConfigEntity } from "./interface"

export class ConversationSearchConfig
    extends BaseSearchConfig
    implements SearchConfigEntity {
    constructor() {
        const sort = [
            new StringSort("updatedAt", MESSAGE_KEYS.SEARCH_CONFIG.SORT_CREATED_DATE),
            new StringSort("lastMessageAt", "api.search_config.sort.created_date"),
        ]
        super([], sort, [], [])
    }
}

export const conversationsSearchConfig = new ConversationSearchConfig()
