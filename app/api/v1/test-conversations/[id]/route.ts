import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { testConversationBusinessLogic } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { createContextLogger } from "@/lib/logging"
import { loggedApiRoute } from "@/lib/logging/middleware"

export const GET = loggedApiRoute(async (
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await params

    const logger = createContextLogger({
      component: 'test-conversations',
      userId: context.user.id,
      organizationId: context.organization?.id
    })

    logger.info('Fetching test conversation', { conversationId: id })

    // Get test conversation directly from testConversationBusinessLogic
    const testConversation = await testConversationBusinessLogic.getById(id, context)
    if (!testConversation) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Test conversation not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
        { status: 404 }
      )
    }

    return NextResponse.json(
      new ResponseWrapper("success", testConversation),
      { status: 200 }
    )
  } catch (error: any) {
    console.error("Test conversation GET error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch test conversation"],
        [ERROR_CODES.FETCH_FAILED]
      ),
      { status: 500 }
    )
  }
})

export const DELETE = loggedApiRoute(async (
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await params

    const logger = createContextLogger({
      component: 'test-conversations',
      userId: context.user.id,
      organizationId: context.organization?.id
    })

    logger.info('Deleting test conversation', { conversationId: id })

    // Verify it's a test conversation
    const conversation = await testConversationBusinessLogic.getById(id, context)
    if (!conversation) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Test conversation not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
        { status: 404 }
      )
    }

    // Delete the test conversation
    await testConversationBusinessLogic.delete(id, context)

    logger.info('Test conversation deleted', { conversationId: id })

    return NextResponse.json(
      new ResponseWrapper("success", { deleted: true }),
      { status: 200 }
    )
  } catch (error: any) {
    console.error("Test conversation DELETE error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete test conversation"],
        [ERROR_CODES.DELETE_FAILED]
      ),
      { status: 500 }
    )
  }
})
