"use client"

import { useLocalization } from "@/localization/functions/client"
import { AuthAPI } from "@/lib/services/authApi"
import { useState } from "react"
import { authLocales } from "../locales"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card"

export default function RegisterPage() {
  const { t } = useLocalization("auth", authLocales)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [name, setName] = useState("")
  const [message, setMessage] = useState<string | null>(null)
  const [isError, setIsError] = useState(false)

  const validateEmail = (email: string) => {
    // Simple email regex check
    return /^\S+@\S+\.\S+$/.test(email)
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage(null)
    setIsError(false)

    if (!name.trim()) {
      setMessage(t("auth.name_required") || "Name is required")
      setIsError(true)
      return
    }
    if (!email.trim()) {
      setMessage(t("auth.email_required") || "Email is required")
      setIsError(true)
      return
    }
    if (!validateEmail(email)) {
      setMessage(t("auth.invalid_email_format") || "Invalid email format")
      setIsError(true)
      return
    }
    if (!password) {
      setMessage(t("auth.password_required") || "Password is required")
      setIsError(true)
      return
    }

    try {
      await AuthAPI.Register({ email, password, name }).request()
      setMessage(t("auth.register_success") || "Registration successful")
      setIsError(false)
      // Optionally redirect after a short delay
      // setTimeout(() => window.location.href = "/auth/login", 2000)
    } catch (err: any) {
      console.log("ERROR", err)
      setMessage(
        err?.message || t("auth.register_error") || "Registration failed",
      )
      setIsError(true)
    }
  }

  return (
    <div
      className="max-w-md mx-auto mt-20 mb-20"
      data-testid="registration-page"
    >
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">
            {t("auth.register")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleRegister}
            className="space-y-4"
            data-testid="register-form"
          >
            <div className="space-y-2">
              <Label htmlFor="name">{t("auth.name")}</Label>
              <Input
                id="name"
                name="name"
                data-testid="name-input"
                placeholder={t("auth.name")}
                value={name}
                onChange={(e) => setName(e.target.value)}
                autoComplete="name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">{t("auth.email")}</Label>
              <Input
                id="email"
                name="email"
                data-testid="email-input"
                placeholder={t("auth.email")}
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                autoComplete="email"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">{t("auth.password")}</Label>
              <Input
                id="password"
                name="password"
                data-testid="password-input"
                placeholder={t("auth.password")}
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                autoComplete="new-password"
              />
            </div>
            <Button
              type="submit"
              data-testid="register-button"
              className="w-full"
            >
              {t("auth.register")}
            </Button>
            {message && (
              <p
                data-testid={isError ? "error-message" : "success-message"}
                className={`text-sm mt-2 ${isError ? "text-destructive" : "text-green-600"}`}
              >
                {message}
              </p>
            )}
          </form>
        </CardContent>
        <CardFooter>
          <div className="text-center w-full">
            <p className="text-sm">
              {t("auth.have_account")}{" "}
              <Link
                href="/auth/login"
                className="text-primary hover:underline"
                data-testid="login-link"
              >
                {t("auth.login")}
              </Link>
            </p>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
