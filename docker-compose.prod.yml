version: '3.8'

services:
  cs-ai-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner  # Use production stage
    ports:
      - "3002:3000"
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    networks:
      - cs-ai-network
    volumes:
      # Production volumes - only mount necessary directories
      - ./logs:/app/logs:rw
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  cs-ai-network:
    driver: bridge
