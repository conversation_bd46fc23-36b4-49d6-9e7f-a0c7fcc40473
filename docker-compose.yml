version: '3.8'

services:
  cs-ai-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3002:3000"
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    networks:
      - cs-ai-network
    volumes:
      # Optional: Mount logs directory if your app writes logs
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  cs-ai-network:
    driver: bridge
